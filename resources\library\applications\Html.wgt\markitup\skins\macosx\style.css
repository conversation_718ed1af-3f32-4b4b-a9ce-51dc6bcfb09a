/* -------------------------------------------------------------------
// markItUp! Universal MarkUp Engine, JQuery plugin
// By <PERSON> - http://markitup.jaysalvat.com/
// ------------------------------------------------------------------*/
body {
/*	background-color:#3875D7;*/
}
.markItUp  {
	background-color:#3875D7;
	border:1px solid #3C769D;
	padding:13px;
}
.markItUp * {
	margin:0px; padding:0px;
	outline:none;
}
.markItUp a:link,
.markItUp a:visited {
	color:#000;
	text-decoration:none;
}
.markItUp  {
	width:670px;
	margin:5px 0 5px 0;
}
.markItUpContainer  {
	width:670px;
	font:11px <PERSON>erd<PERSON>, Arial, Helvetica, sans-serif;
	background-image:url(images/bg-container.png);
}
.markItUpEditor {
	font:12px 'Courier New', Courier, monospace;
	padding:5px;
	margin:0px 11px 0px 11px;
	width:640px;
	height:320px;
	clear:both; display:block;
	line-height:18px;
	overflow:auto;
	border:0px solid white;
}
.markItUpPreviewFrame	{
	overflow:auto;
	background-color:#FFFFFF;
	width:99.9%;
	height:350px;
	margin:5px 0;
}
.markItUpHeader {
	width:650px;
	padding:15px 0px 0px 20px;
	height:25px;
	background-image:url(images/bg-header.png);
}
.markItUpFooter {
	width:670px;
	height:25px;
	background-image:url(images/bg-footer.png);
	padding-bottom:10px;
}
.markItUpResizeHandle {
	margin:0px 12px 5px auto;
	overflow:hidden;
	width:12px; height:12px;
	position:relative;
	background-image:url(images/handle.png);
	cursor:n-resize;
}
/***************************************************************************************/
/* first row of buttons */
.markItUpHeader ul li	{
	list-style:none;
	float:left;
	position:relative;
}
.markItUpHeader ul li:hover > ul{
	display:block;
}
.markItUpHeader ul .markItUpDropMenu {
	background:transparent url(images/menu.png) no-repeat 115% 50%;
	margin-right:5px;
}
.markItUpHeader ul .markItUpDropMenu li {
	margin-right:0px;
}
/* next rows of buttons */
.markItUpHeader ul ul {
	display:none;
	position:absolute;
	top:18px; left:0px;	
	background:#FFF;
	border:1px solid #000;
}
.markItUpHeader ul ul li {
	float:none;
	border-bottom:1px solid #000;
}
.markItUpHeader ul ul .markItUpDropMenu {
	background:#FFF url(images/submenu.png) no-repeat 100% 50%;
}
.markItUpHeader ul .markItUpSeparator {
	margin:0 10px;
	width:1px;
	height:16px;
	overflow:hidden;
	background-color:#CCC;
}
.markItUpHeader ul ul .markItUpSeparator {
	width:auto; height:1px;
	margin:0px;
}
/* next rows of buttons */
.markItUpHeader ul ul ul {
	position:absolute;
	top:-1px; left:150px; 
}
.markItUpHeader ul ul ul li {
	float:none;
}
.markItUpHeader ul a {
	display:block;
	width:16px; height:16px;
	text-indent:-10000px;
	background-repeat:no-repeat;
	padding:3px;
	margin:0px;
}
.markItUpHeader ul ul a {
	display:block;
	padding-left:0px;
	text-indent:0;
	width:120px; 
	padding:5px 5px 5px 25px;
	background-position:2px 50%;
}
.markItUpHeader ul ul a:hover  {
	color:#FFF;
	background-color:#3875D7;
}