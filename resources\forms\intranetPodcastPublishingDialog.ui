<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>IntranetPodcastPublishingDialog</class>
 <widget class="QDialog" name="IntranetPodcastPublishingDialog">
  <property name="windowModality">
   <enum>Qt::WindowModal</enum>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>540</width>
    <height>303</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Publish to Intranet</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QFormLayout" name="formLayout">
     <property name="fieldGrowthPolicy">
      <enum>QFormLayout::AllNonFixedFieldsGrow</enum>
     </property>
     <item row="0" column="0">
      <widget class="QLabel" name="titleLabel">
       <property name="text">
        <string>Title</string>
       </property>
      </widget>
     </item>
     <item row="0" column="1">
      <widget class="QLineEdit" name="title">
       <property name="maxLength">
        <number>60</number>
       </property>
      </widget>
     </item>
     <item row="2" column="0">
      <widget class="QLabel" name="decriptionLabel">
       <property name="text">
        <string>Description</string>
       </property>
      </widget>
     </item>
     <item row="2" column="1">
      <widget class="QPlainTextEdit" name="description">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Expanding" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="minimumSize">
        <size>
         <width>0</width>
         <height>168</height>
        </size>
       </property>
      </widget>
     </item>
     <item row="4" column="0">
      <widget class="QLabel" name="keywordsLabel">
       <property name="text">
        <string>Author</string>
       </property>
      </widget>
     </item>
     <item row="4" column="1">
      <widget class="QLineEdit" name="author">
       <property name="text">
        <string/>
       </property>
      </widget>
     </item>
     <item row="5" column="1">
      <widget class="QDialogButtonBox" name="dialogButtons">
       <property name="standardButtons">
        <set>QDialogButtonBox::Cancel|QDialogButtonBox::Ok</set>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <resources/>
 <connections/>
</ui>
