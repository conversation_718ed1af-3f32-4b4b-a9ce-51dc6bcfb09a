<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8">
    <title>Camera</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="cropper/cropper.css">
    <script src="js/camera.js"></script>
    <script src="js/i18n.js"></script>
    <script src="cropper/cropper.js"></script>
  </head>
  <body onload="init();">
    <video autoplay id="video"></video>
    <img id="glass" src="img/glass.png"/>
    <div class="container">
      <div class="cell">
        <div class="content">
          <div class="nocamera" id="nocamera">
            <img src="img/nocamera.svg"/>
          </div>
          <div class="playpause" id="playpause">
            <img class="mediabutton" id="play" src="img/play.svg" draggable="false"/>
            <img class="mediabutton" id="pause" src="img/pause.svg" draggable="false"/>
          </div>
          <div class="buttons overlay-left" id="buttons-left">
            <button class="button tooltip" id="rotleft">
              <img class="icon" src="img/rotleft.svg"/>
              <span class="tooltiptext tooltipright">Rotate left</span>
            </button>
            <button class="button tooltip" id="rotright">
              <img class="icon" src="img/rotright.svg"/>
              <span class="tooltiptext tooltipright">Rotate right</span>
            </button>
          </div>
          <div class="buttons overlay" id="buttons">
            <button class="button tooltip" id="snapshot">
              <img class="icon" src="img/snapshot.svg"/>
              <span class="tooltiptext tooltipleft">Take snapshot</span>
            </button>
            <div class="dropdown">
              <button class="button tooltip" id="selectSize">
                <div class="icon" id="sizeIcon">M</div>
                <span class="tooltiptext tooltipleft">Snapshot size</span>
              </button>
              <div class="dropdown-content" id="sizeSelect">
              </div>
            </div>
            <div class="dropdown">
              <button class="button tooltip" id="selectCamera">
                <img class="icon" src="img/select-camera.svg"/>
                <span class="tooltiptext tooltipleft">Select camera</span>
              </button>
              <div class="dropdown-content" id="videoSelect">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <canvas class="is-hidden" id="canvas"></canvas>
  </body>
</html>
