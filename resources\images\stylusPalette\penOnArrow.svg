<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   width="42"
   height="42"
   viewBox="0 0 42 42"
   version="1.1"
   id="svg38508"
   inkscape:version="1.1 (c68e22c387, 2021-05-23)"
   sodipodi:docname="penOnArrow.svg"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:cc="http://creativecommons.org/ns#">
  <sodipodi:namedview
     id="namedview38510"
     pagecolor="#505050"
     bordercolor="#eeeeee"
     borderopacity="1"
     inkscape:pageshadow="0"
     inkscape:pageopacity="0"
     inkscape:pagecheckerboard="0"
     inkscape:document-units="px"
     showgrid="false"
     fit-margin-top="0"
     fit-margin-left="0"
     fit-margin-right="0"
     fit-margin-bottom="0"
     units="px"
     inkscape:zoom="8.7988368"
     inkscape:cx="-8.2397255"
     inkscape:cy="7.216863"
     inkscape:window-width="2560"
     inkscape:window-height="1357"
     inkscape:window-x="2560"
     inkscape:window-y="0"
     inkscape:window-maximized="1"
     inkscape:current-layer="g1275"
     scale-x="1" />
  <defs
     id="defs38505">
    <linearGradient
       inkscape:collect="always"
       id="gradientBackgroundCircle">
      <stop
         style="stop-color:#ffffff;stop-opacity:0"
         offset="0"
         id="stop39510" />
      <stop
         style="stop-color:#ffffff;stop-opacity:1"
         offset="1"
         id="stop39512" />
    </linearGradient>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#gradientBackgroundCircle"
       id="radialGradient39516-8-7"
       cx="160.25581"
       cy="201.69054"
       fx="160.25581"
       fy="201.69054"
       r="7.2760415"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.76363638,0,0,0.76363638,36.02656,45.82022)" />
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter1048"
       x="-0.029090904"
       y="-0.028602066"
       width="1.0690121"
       height="1.0678525"
       inkscape:label="shadowFilter">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="1.1191913"
         id="feGaussianBlur1050" />
      <feOffset
         dx="1"
         dy="1"
         id="feOffset876" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#gradientStylusHolder"
       id="linearGradient11809"
       x1="110.36134"
       y1="192.05977"
       x2="117.9259"
       y2="192.05977"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.3077232,0,0,0.3077232,120.76812,143.13922)" />
    <linearGradient
       inkscape:collect="always"
       id="gradientStylusHolder">
      <stop
         style="stop-color:#a7a7a7;stop-opacity:1;"
         offset="0"
         id="stop9704" />
      <stop
         style="stop-color:#212121;stop-opacity:1"
         offset="1"
         id="stop9706" />
    </linearGradient>
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter12705-7"
       x="-0.12427052"
       y="-0.099978448"
       width="1.248541"
       height="1.1999569"
       inkscape:label="filterBottomGlare">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.27371099"
         id="feGaussianBlur12707-6" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#gradientBody"
       id="linearGradient12481"
       x1="113.45532"
       y1="190.96683"
       x2="118.70955"
       y2="190.96683"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.3077232,0,0,0.3077232,120.76812,143.13922)" />
    <linearGradient
       inkscape:collect="always"
       id="gradientBody">
      <stop
         style="stop-color:#ff0000;stop-opacity:1"
         offset="0"
         id="stop6588" />
      <stop
         style="stop-color:#2b0000;stop-opacity:1"
         offset="1"
         id="stop6590" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#gradientButtonAndPin"
       id="linearGradient8002"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.22058068,0,0,0.22058068,225.38794,0.08238655)"
       x1="115.95115"
       y1="174.19702"
       x2="121.0631"
       y2="174.19702" />
    <linearGradient
       inkscape:collect="always"
       id="gradientButtonAndPin">
      <stop
         style="stop-color:#ffffff;stop-opacity:1"
         offset="0"
         id="stop6886" />
      <stop
         style="stop-color:#767676;stop-opacity:1"
         offset="1"
         id="stop6888" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#gradientBody"
       id="linearGradient6594-9"
       x1="106.14433"
       y1="128.42783"
       x2="108.69588"
       y2="130.97939"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.3077232,0,0,0.3077232,126.44178,159.50234)" />
    <filter
       inkscape:collect="always"
       style="color-interpolation-filters:sRGB"
       id="filter40862-5"
       x="-0.036175182"
       y="-0.037975627"
       width="1.0723504"
       height="1.0759513"
       inkscape:label="filterTopGlare">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="0.23570633"
         id="feGaussianBlur40864-4" />
    </filter>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#gradientButtonAndPin"
       id="linearGradient6790"
       x1="215.25211"
       y1="34.826889"
       x2="217.52344"
       y2="34.805481"
       gradientUnits="userSpaceOnUse"
       gradientTransform="matrix(0.30772323,0,0,0.30772323,186.63225,15.468073)" />
  </defs>
  <g
     inkscape:label="icon"
     inkscape:groupmode="layer"
     id="layer1"
     transform="translate(-152.84748,-194.2822)">
    <g
       id="g1275"
       inkscape:label="penOnArrow"
       transform="matrix(3.7795275,0,0,3.7795275,-424.84378,-540.01273)">
      <circle
         style="display:inline;fill:url(#radialGradient39516-8-7);fill-opacity:1;stroke-width:0.0120993;stroke-linecap:round;stroke-linejoin:round"
         id="path38960-7-4"
         cx="158.40373"
         cy="199.83846"
         r="5.5562501"
         inkscape:label="circleBackground" />
      <path
         style="fill:#000000;fill-opacity:1;stroke:none;stroke-width:0.394793px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
         d="m 161.8435,204.86553 1.8519,-1.71519 -1.85209,-1.72439 c 0.002,1.14652 4.4e-4,2.29307 1.9e-4,3.43958 z"
         id="path41415-8"
         sodipodi:nodetypes="cccc"
         inkscape:label="arrow" />
      <path
         style="fill:#7a7a7a;fill-opacity:0.866667;stroke:none;stroke-width:0.165;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:4;stroke-dasharray:none;stroke-opacity:1"
         d="m 110.08855,196.6511 c 0,0 5.72311,2.09044 13.2523,1.18014 3.20066,-0.38697 3.71249,-1.81938 2.71369,-2.80624 -1.18663,-1.17244 -3.23895,-2.33431 -3.33092,-2.95537 -0.24432,-1.64982 6.26522,-0.39134 7.96716,-0.22527 -2.86832,-1.74577 -5.56785,-1.84283 -6.10094,-1.88234 -0.51067,-0.0378 -3.47751,-0.24754 -4.14947,1.24553 -0.73609,1.63558 1.9623,2.90055 1.9623,2.90055 0,0 3.35403,1.59888 -1.15991,2.30414 -3.77103,0.58919 -11.15421,0.23886 -11.15421,0.23886 z"
         id="path12742-7"
         sodipodi:nodetypes="cssscsscsc"
         transform="matrix(0.34189008,0,0,0.30738626,117.00811,143.2046)"
         inkscape:label="pathInk" />
      <path
         id="path1011"
         style="mix-blend-mode:normal;fill:#333333;fill-opacity:1;stroke:none;stroke-width:1.09112px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;filter:url(#filter1048)"
         d="m -25.039062,2.1191406 c -0.369462,-5.738e-4 -0.656163,0.096083 -0.839844,0.3007813 l -5.169922,5.7597656 c -0.0805,0.089709 -0.137721,0.1971749 -0.173828,0.3203125 -1.215346,-0.2757137 -2.344201,-0.1217399 -3.638672,0.5761719 l -57.369141,56.3828121 -0.955078,2.976563 c -10.707303,11.355449 -11.801823,20.604589 -11.703123,24.427734 l -1.03711,3.066407 c 0,0 0.43852,0.60109 3.07031,-1.132813 11.743344,-0.236 21.551721,-9.1771 25.111329,-12.871094 l 0.0039,-0.002 2.388672,-1.167969 56.371093,-59.539062 c -0.0072,-0.616662 -0.119796,-1.290586 -0.324219,-1.992188 0.128604,-0.04964 0.236596,-0.121324 0.322266,-0.216797 L -13.812507,13.248 c 0.904276,-1.007746 -1.05999,-4.2351957 -4.402344,-7.2343751 -2.663438,-2.3899711 -5.374792,-3.8922799 -6.824218,-3.8945313 z"
         transform="matrix(0.08141844,0,0,0.08141844,163.26923,195.84286)"
         inkscape:label="shadow" />
      <path
         style="fill:url(#linearGradient11809);fill-opacity:1;stroke:none;stroke-width:0.0888376px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
         d="m 154.74447,203.55833 c 0,0 -0.22694,-0.98201 1.05103,-2.26009 1.27797,-1.27805 1.26115,1.08663 1.26115,1.08663 0,0 -1.03653,1.24925 -2.31218,1.17346 z"
         id="path9606"
         sodipodi:nodetypes="cccc"
         inkscape:label="blackBlur" />
      <path
         style="fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:0.264583px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;filter:url(#filter12705-7)"
         d="m 109.86907,196.12316 3.79766,-6.57048 1.4884,1.10567 -5.2861,5.46481 z"
         id="path12389-5"
         transform="matrix(0.33413223,0,0,0.3133411,117.96516,142.11697)"
         inkscape:label="bottomGlare" />
      <path
         style="fill:#302300;fill-opacity:1;stroke:none;stroke-width:0.0814186px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
         d="m 154.73498,203.38735 c 0,0 0.146,0.0454 0.17015,0.16699 -0.2233,0.14945 -0.26019,0.099 -0.26019,0.099 z"
         id="path38159-5"
         sodipodi:nodetypes="cccc"
         inkscape:label="stylus" />
      <path
         style="fill:url(#linearGradient12481);fill-opacity:1;stroke:none;stroke-width:0.0814186px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
         d="m 155.76005,201.17237 -0.0792,0.24657 c 0,0 0.66256,0.27659 1.25844,1.09421 0.50207,0.68885 0,0 0,0 l 0.19489,-0.0953 -0.0789,-0.99598 -0.9422,-0.23881 z"
         id="path12399"
         sodipodi:nodetypes="ccsccccc"
         inkscape:label="bevel" />
      <rect
         style="fill:url(#linearGradient8002);fill-opacity:1;stroke:none;stroke-width:0.0363957;stroke-linecap:round;stroke-linejoin:round"
         id="rect7697"
         width="1.3199494"
         height="1.0281007"
         x="250.87828"
         y="38.075741"
         ry="0.19898757"
         transform="rotate(41.902436)"
         rx="0.65997469"
         inkscape:label="button" />
      <path
         style="fill:url(#linearGradient6594-9);fill-opacity:1;stroke:none;stroke-width:0.0814186px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1"
         d="m 155.76005,201.17237 4.67085,-4.59053 c 0.24894,-0.13422 0.42256,-0.0237 0.79178,0.21994 0.32682,0.21559 0.49851,0.5392 0.50119,0.76853 l -4.58955,4.84759 c -0.15837,-0.52073 -0.80391,-1.05701 -1.37427,-1.24553 z"
         id="path55-2-5"
         sodipodi:nodetypes="ccsccc"
         inkscape:label="body" />
      <path
         style="mix-blend-mode:normal;fill:#ffffff;fill-opacity:0.922547;stroke:none;stroke-width:0.264583px;stroke-linecap:butt;stroke-linejoin:miter;stroke-opacity:1;filter:url(#filter40862-5)"
         d="m 132.26583,204.41411 0.77298,0.46621 14.86468,-14.59556 -1.08253,-0.30071 z"
         id="path40464-3"
         sodipodi:nodetypes="ccccc"
         transform="matrix(0.30772324,0,0,0.30772324,115.58223,138.46509)"
         inkscape:label="topGlare" />
      <rect
         style="fill:url(#linearGradient6790);fill-opacity:1;stroke:none;stroke-width:0.0507743;stroke-linecap:round;stroke-linejoin:round"
         id="rect6666"
         width="0.6662389"
         height="3.1461287"
         x="252.8703"
         y="24.612047"
         transform="rotate(45.079262)"
         ry="0.17010193"
         inkscape:label="pin" />
    </g>
  </g>
  <metadata
     id="metadata1252">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <cc:license
           rdf:resource="http://creativecommons.org/publicdomain/zero/1.0/" />
      </cc:Work>
      <cc:License
         rdf:about="http://creativecommons.org/publicdomain/zero/1.0/">
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#Reproduction" />
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#Distribution" />
        <cc:permits
           rdf:resource="http://creativecommons.org/ns#DerivativeWorks" />
      </cc:License>
    </rdf:RDF>
  </metadata>
</svg>
