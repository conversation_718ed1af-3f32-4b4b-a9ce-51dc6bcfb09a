
/*** ESSENTIAL STYLES ***/
.sf-menu, .sf-menu * {
    margin:         0;
    padding:        0;
    list-style:     none;
}
.sf-menu {
    line-height:    1.0;
    position:relative;
    display:table;
}
.sf-menu ul {
    position:       absolute;
    top:            -999em;
    width:          80px; /* left offset of submenus need to match (see below) */
    background-color:#ffffff;
}
.sf-menu ul li {
    width:          100%;
}
.sf-menu li:hover {
    visibility:     inherit; /* fixes IE7 'sticky bug' */
}
.sf-menu li {
  display:table-cell;
  vertical-align:middle;
    position:       relative;
    padding-left:3px;
    width:61px;
    height:22px;
}

.sf-menu a {
    display:        block;
    position:       relative;
}
.sf-menu li:hover ul,
.sf-menu li.sfHover ul {
    left:           -20px;
    top:            22px; /* match top ul list item height */
    z-index:        99;
}
ul.sf-menu li:hover li ul,
ul.sf-menu li.sfHover li ul {
    top:            -999em;
}
ul.sf-menu li li:hover ul,
ul.sf-menu li li.sfHover ul {
    left:           10em; /* match ul width */
    top:            0;
}
ul.sf-menu li li:hover li ul,
ul.sf-menu li li.sfHover li ul {
    top:            -999em;
}
ul.sf-menu li li li:hover ul,
ul.sf-menu li li li.sfHover ul {
    left:           200px; /* match ul width */
    top:            0;
}

/*** DEMO SKIN ***/
.sf-menu {
    float:          left;
    font-family:arial, sans-serif;
    font-size:12px;
    margin-top:1px;
}

.sf-menu li ul{
  border:1px solid #ccc;
  padding-top:6px;
  padding-right:3px;
}

.sf-menu a {
    padding: .1em;
    padding-left:.5em;
    text-decoration:none;
}
.sf-menu li ul li a{
    padding: 1px 0px 4px 6px;
}
.sf-menu li ul li a.sf-sl-last{
}
.sf-menu li ul li a.sf-sl-first{

}
.sf-menu a, .sf-menu a:visited  { /* visited pseudo selector so IE6 applies text colour*/
    color:          #000;
}
.sf-menu li {
}
.sf-menu li .selected{
    font-weight:        bold;
    color:#333;
    text-shadow:#fff 0px 1px 1px;
}
.sf-menu li li {
    background:     #FFF;
    float:left;
}
.sf-menu li li li {
    background:     #FFF;
}
.sf-menu li li:hover {
    outline:        0;
    background:     #e7edf7;
}
/*** arrows **/
.sf-menu a.sf-with-ul {
    padding-right:  2.25em;
    min-width:      1px; /* trigger IE7 hasLayout so spans position accurately */
}
.sf-sub-indicator {
    position:       absolute;
    display:        block;
    right:          .75em;
    top:            1.05em; /* IE6 only */
    width:          10px;
    height:         10px;
    text-indent:    -999em;
    overflow:       hidden;
}
a > .sf-sub-indicator {  /* give all except IE6 the correct values */
    top:            .2em;
    background-position: 0 -100px; /* use translucent arrow for modern browsers*/
}
/* apply hovers to modern browsers */
a:focus > .sf-sub-indicator,
a:hover > .sf-sub-indicator,
a:active > .sf-sub-indicator,
li:hover > a > .sf-sub-indicator,
li.sfHover > a > .sf-sub-indicator {
    background-position: -10px -100px; /* arrow hovers for modern browsers*/
}

/* point right for anchors in subs */
.sf-menu ul .sf-sub-indicator { background-position:  -10px 0; }
.sf-menu ul a > .sf-sub-indicator { background-position:  0 0; }
/* apply hovers to modern browsers */
.sf-menu ul a:focus > .sf-sub-indicator,
.sf-menu ul a:hover > .sf-sub-indicator,
.sf-menu ul a:active > .sf-sub-indicator,
.sf-menu ul li:hover > a > .sf-sub-indicator,
.sf-menu ul li.sfHover > a > .sf-sub-indicator {
    background-position: -10px 0; /* arrow hovers for modern browsers*/
}

/*** shadows for all but IE6 ***/
.sf-shadow ul {
    background: url('../images/shadow.png') no-repeat bottom right;
    padding: 0 8px 9px 0;
    -moz-border-radius-bottomleft: 17px;
    -moz-border-radius-topright: 17px;
    -webkit-border-top-right-radius: 17px;
    -webkit-border-bottom-left-radius: 17px;
}
.sf-shadow ul.sf-shadow-off {
    background: transparent;
}