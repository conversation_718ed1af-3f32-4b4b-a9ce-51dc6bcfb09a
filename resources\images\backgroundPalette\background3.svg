<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 13.0.2, SVG Export Plug-In . SVG Version: 6.00 Build 14948)  -->
<!DOCTYPE svg [
	<!ENTITY ns_flows "http://ns.adobe.com/Flows/1.0/">
	<!ENTITY ns_extend "http://ns.adobe.com/Extensibility/1.0/">
	<!ENTITY ns_ai "http://ns.adobe.com/AdobeIllustrator/10.0/">
	<!ENTITY ns_graphs "http://ns.adobe.com/Graphs/1.0/">
	<!ENTITY ns_vars "http://ns.adobe.com/Variables/1.0/">
	<!ENTITY ns_imrep "http://ns.adobe.com/ImageReplacement/1.0/">
	<!ENTITY ns_sfw "http://ns.adobe.com/SaveForWeb/1.0/">
	<!ENTITY ns_custom "http://ns.adobe.com/GenericCustomNamespace/1.0/">
	<!ENTITY ns_adobe_xpath "http://ns.adobe.com/XPath/1.0/">
]>
<svg version="1.2" baseProfile="tiny" xmlns:x="&ns_extend;" xmlns:i="&ns_ai;" xmlns:graph="&ns_graphs;"
	 xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:a="http://ns.adobe.com/AdobeSVGViewerExtensions/3.0/"
	 x="0px" y="0px" width="128px" height="128px" viewBox="0 0 128 128" xml:space="preserve">
<metadata>
	<sfw  xmlns="&ns_sfw;">
		<slices></slices>
		<sliceSourceBounds  x="8.255" y="22.924" width="110.488" height="82.884" bottomLeftOrigin="true"></sliceSourceBounds>
	</sfw>
</metadata>
<g id="Layer_2">
	<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="63.3647" y1="24.7017" x2="63.3647" y2="102.1488">
		<stop  offset="0" style="stop-color:#4D4D4D"/>
		<stop  offset="1" style="stop-color:#000000"/>
		<a:midPointStop  offset="0" style="stop-color:#4D4D4D"/>
		<a:midPointStop  offset="0.5" style="stop-color:#4D4D4D"/>
		<a:midPointStop  offset="1" style="stop-color:#000000"/>
	</linearGradient>
	<path fill="url(#SVGID_1_)" d="M104.771,102.666H21.957c-6.19,0-11.227-5.037-11.227-11.23V36.227
		C10.73,30.036,15.766,25,21.957,25h82.815C110.963,25,116,30.036,116,36.227v55.208C116,97.629,110.963,102.666,104.771,102.666
		L104.771,102.666z"/>
	<linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="66.1289" y1="104.8428" x2="66.1289" y2="77.0022">
		<stop  offset="0" style="stop-color:#E0E0E0"/>
		<stop  offset="1" style="stop-color:#999999"/>
		<a:midPointStop  offset="0" style="stop-color:#E0E0E0"/>
		<a:midPointStop  offset="0.5" style="stop-color:#E0E0E0"/>
		<a:midPointStop  offset="1" style="stop-color:#999999"/>
	</linearGradient>
	<path fill="url(#SVGID_2_)" d="M113.525,71.782c0,0,0.02-0.042,0.052-0.114C113.559,71.709,113.543,71.742,113.525,71.782z
		 M113.525,71.782c-5.221,11.31-14.791,12.181-14.791,12.181l-81.273,13.92l-3.946,4.193c2.36,1.872,5.337,3,8.577,3h82.815
		c7.629,0,13.837-6.207,13.837-13.84V71.782H113.525z"/>
	<path fill="#FFFFFF" d="M104.906,22.192c7.629,0,13.837,6.207,13.837,13.836v55.208c0,7.633-6.208,13.84-13.837,13.84H22.091
		c-7.629,0-13.836-6.207-13.836-13.84V36.028c0-7.629,6.207-13.836,13.836-13.836H104.906 M104.906,27.412H22.091
		c-4.751,0-8.616,3.865-8.616,8.616v55.208c0,4.753,3.865,8.619,8.616,8.619h82.815c4.751,0,8.617-3.866,8.617-8.619V36.028
		C113.523,31.277,109.657,27.412,104.906,27.412L104.906,27.412z"/>
	<path fill="#B0B9C4" d="M104.906,22.192c7.629,0,13.837,6.207,13.837,13.836v55.208c0,7.633-6.208,13.84-13.837,13.84H22.091
		c-7.629,0-13.836-6.207-13.836-13.84V36.028c0-7.629,6.207-13.836,13.836-13.836H104.906 M104.906,25.672H22.091
		c-5.71,0-10.356,4.646-10.356,10.356v55.208c0,5.713,4.646,10.359,10.356,10.359h82.815c5.711,0,10.357-4.646,10.357-10.359V36.028
		C115.264,30.318,110.617,25.672,104.906,25.672L104.906,25.672z"/>
</g>
<g id="Layer_1">
	<linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="68.6816" y1="92.8516" x2="63.4618" y2="78.9319">
		<stop  offset="0" style="stop-color:#657180"/>
		<stop  offset="1" style="stop-color:#AFB9C5"/>
		<a:midPointStop  offset="0" style="stop-color:#657180"/>
		<a:midPointStop  offset="0.5" style="stop-color:#657180"/>
		<a:midPointStop  offset="1" style="stop-color:#AFB9C5"/>
	</linearGradient>
	<path fill="url(#SVGID_3_)" d="M96.125,87.346C79.1,91.045,17.461,97.883,17.461,97.883l70.542-26.666
		c0,0,20.494,17.185,25.521-0.401C113.525,70.815,113.148,83.646,96.125,87.346z"/>
</g>
</svg>
