<!--
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program.  If not, see <http://www.gnu.org/licenses/>.
-->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
    "http://www.w3.org/TR/html4/loose.dtd">

<html>

    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>Video Picker</title>
        <link rel="stylesheet" type="text/css" href="css/main.css">
        <script src="scripts/jquery-1.3.2.min.js" type="text/javascript"></script>
        <script src="scripts/languages.js" type="text/javascript"></script>
        <script src="scripts/jquery.oembed.js" type="text/javascript"></script>
        <!--DEFAULT FR-->
    </head>

    <body>
        <script type="text/javascript">

            var oembedUrl = "";
            var lang = "en";
                
            if(window.sankore){
                try{
                    lang = sankore.lang.substr(0,2);
                    sankoreLang[lang].show;
                } catch(e){
                    lang = "en";
                }
            } else 
                lang = "en";
		
            async function initialize() {
                if(window.sankore){
                    oembedUrl = await window.sankore.async.preference("oembedUrl", "");
                }
			
                window.resizeTo(426, 630);
                if(oembedUrl.length > 0){
                    $("#textbox").val(oembedUrl);	
                    $("#search-button").trigger("click");
                }else{                    	
                    $("#embeded-content").load("locales/" + lang + "/howto.html"); // Welcome note with some explanations
                };
            };
		
            $(document).ready(function(){
                var resizer; // Timer
                var resizerIndex;
			
                resizerIndex = 0;
			
                $("#search-button")
                .click(function(){
                    if($("#textbox").val()){
                        window.resizeTo(385, 80);
                        $("#container").removeClass("welcome").removeClass("change").removeClass("error").addClass("load");
                        $("#container-shadow").hide();
                        $("#arrow").hide();
                        $("#embeded-content").css({
                            marginLeft:-9000,
                            marginTop:-9000
                        }).empty().oembed($("#textbox").val());					
                        checkcontent();
                    } else {
                        var sleepTime = 100;
                        var counter = 0;
                        var id = setInterval(function(){
                            $("#textbox").toggleClass("bordered1").toggleClass("bordered2");
                            counter ++;
                            if(counter == 10)
                                clearInterval(id);
                        }, sleepTime);
                    }
                })
                .mouseenter(function(){
                    $(this)
                    .addClass("over");
                })
                .mouseleave(function(){
                    $(this)
                    .removeClass("over");
                });
			
                $('#textbox').keypress(function(e){
                    if (e.which == 13){
                        $('#search-button').click();
                    };
                });
			
                $("#show-inputfield-button")
                .click(
                function(){
                    $("#show-area").fadeOut(0);
                    $("#inputfield").show();
                    $("#shadow").show();
                    $("#container").removeClass("show").addClass("change");
                    adaptWidgetSize(18, 88);
                })
                .mouseenter(function(){
                    $(this).addClass("over");
                })
                .mouseleave(function(){
                    $(this).removeClass("over");
                });
							
                function checkcontent(){
                    if($("#embeded-content").html().length != 0 && $("#embeded-content").children(":first").width() > 0){
                        $("#embeded-content embed").attr("wmode", "opaque");
                        var embeded_content_html = $("#embeded-content").html();
                        $("#embeded-content").empty().html(embeded_content_html);		
                        clearTimeout(resizer);
                        resizerIndex = 0;
															
                        if(window.sankore){
                            window.sankore.setPreference("oembedUrl", $("#textbox").val());
                        };
					
                        $("#inputfield").hide();
                        $("#shadow").hide();
                        $("#embeded-content").css({
                            marginLeft:0,
                            marginTop:0
                        })
                        .width($("#embeded-content").children(":first").width())
                        .height($("#embeded-content").children(":first").height());
                        $("#container").removeClass("load").addClass("show");
                        $("#show-area").fadeIn(2000).css({
                            left:$("#embeded-content").children(":first").width()-25,
                            top:($("#embeded-content").children(":first").height()-35)/2
                        });
										
                        adaptWidgetSize(3, 0);					
                        return false;
                    };
				
                    if(resizerIndex > 50){
                        resizerIndex = 0;
                        $("#container").removeClass("show").addClass("error");
                        $("#arrow").show();
                        $("#embeded-content").css({
                            marginTop:0,
                            marginLeft:0
                        }).width(395)
                        .load("locales/" + lang + "/error.html", 
                        function(){
                            adaptWidgetSize(20, 125);
                        });
                        $("#container-shadow").show();
                    }else{
                        resizer = setTimeout(function(){checkcontent()}, 100);
                        resizerIndex++;
                    };
                };
			
                function adaptWidgetSize(adjustmentX, adjustmentY){
                    var margins = {
                        top: cssToInt($("body").css("margin-top")), 
                        right: cssToInt($("body").css("margin-right")), 
                        bottom: cssToInt($("body").css("margin-bottom")),
                        left: cssToInt($("body").css("margin-left"))
                    };
                    var widgetWidth = $("#embeded-content").width() + margins.right + margins.left + adjustmentX;
                    var widgetHeight = $("#embeded-content").position().top + $("#embeded-content").height() + margins.bottom + adjustmentY;
				
                    window.resizeTo(widgetWidth, widgetHeight);
                    window.sankore.resize(widgetWidth, widgetHeight);
                };
				
                function cssToInt(cssvalue){
                    return(parseInt(cssvalue.replace("px", "")));
                };
			
                if (window.widget) {
                    window.widget.onremove.connect(() => {
                        $("#embeded-content").empty();
                    });
                }
                $("#search-button").html(sankoreLang[lang].show);
                initialize();
            });
        </script>

        <div id="ubwidget">
            <div id="shadow"></div>

            <div id="inputfield">
                <input id="textbox" type="text" class="bordered2">
                <div id="search-button" class="button">Show</div>
            </div>

            <div id="container" class="welcome">
                <div id="arrow"><img src="imgs/arrow.png" alt="arrow-top"></div>

                <div id="show-container">
                    <div id="embeded-content"></div>

                    <div id="show-area">
                        <img id="show-inputfield-button" src="imgs/bts.png">
                    </div>
                </div>

                <table cellspacing="0" cellpadding="0" id="container-shadow">
                    <tr>
                        <td id="shadow-left"></td>
                        <td id="shadow-center">&nbsp;</td>
                        <td id="shadow-right"></td>
                    </tr>
                </table>
            </div>

        </div>
    </body>

</html>
