import React, { useState } from 'react';
import { PresentationViewer } from './PresentationViewer';

export const PresentationButton: React.FC = () => {
  const [showViewer, setShowViewer] = useState(false);

  return (
    <>
      <button
        className="presentation-button"
        onClick={() => setShowViewer(true)}
        title="Open Google Slides Viewer"
        style={{
          background: '#4285f4',
          color: 'white',
          border: 'none',
          borderRadius: '6px',
          padding: '8px 12px',
          cursor: 'pointer',
          fontSize: '14px',
          fontWeight: '500',
          display: 'flex',
          alignItems: 'center',
          gap: '6px',
          transition: 'background-color 0.2s ease',
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.background = '#3367d6';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.background = '#4285f4';
        }}
      >
        📊 Google Slides
      </button>
      
      <PresentationViewer
        isVisible={showViewer}
        onClose={() => setShowViewer(false)}
      />
    </>
  );
};
