<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>Hierarchy</key>
	<dict>
		<key>Attributes</key>
		<dict>
			<key>Documents</key>
			<dict>
				<key>Background Image</key>
				<dict>
					<key>IFPkgFlagBackgroundAlignment</key>
					<integer>4</integer>
					<key>IFPkgFlagBackgroundScaling</key>
					<integer>1</integer>
					<key>Mode</key>
					<integer>0</integer>
					<key>Path</key>
					<string></string>
					<key>Path Type</key>
					<integer>1</integer>
				</dict>
				<key>License</key>
				<dict>
					<key>International</key>
					<dict>
						<key>Mode</key>
						<integer>1</integer>
						<key>Path</key>
						<string>LICENSE</string>
						<key>Path Type</key>
						<integer>2</integer>
					</dict>
				</dict>
				<key>ReadMe</key>
				<dict>
					<key>International</key>
					<dict>
						<key>Mode</key>
						<integer>0</integer>
						<key>Path</key>
						<string></string>
						<key>Path Type</key>
						<integer>1</integer>
					</dict>
				</dict>
				<key>Welcome</key>
				<dict>
					<key>International</key>
					<dict>
						<key>Mode</key>
						<integer>0</integer>
						<key>Path</key>
						<string></string>
						<key>Path Type</key>
						<integer>1</integer>
					</dict>
				</dict>
			</dict>
			<key>Files</key>
			<dict>
				<key>Compress</key>
				<true/>
				<key>Hierarchy</key>
				<dict>
					<key>Children</key>
					<array>
						<dict>
							<key>Children</key>
							<array>
								<dict>
									<key>Children</key>
									<array/>
									<key>GID</key>
									<integer>80</integer>
									<key>Path</key>
									<string>build/macx/release/product/OpenBoard.app</string>
									<key>Path Type</key>
									<integer>2</integer>
									<key>Privileges</key>
									<integer>493</integer>
									<key>Type</key>
									<integer>3</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>Children</key>
									<array/>
									<key>GID</key>
									<integer>80</integer>
									<key>Path</key>
									<string>Utilities</string>
									<key>Path Type</key>
									<integer>1</integer>
									<key>Privileges</key>
									<integer>509</integer>
									<key>Type</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
							</array>
							<key>GID</key>
							<integer>80</integer>
							<key>Path</key>
							<string>Applications</string>
							<key>Path Type</key>
							<integer>1</integer>
							<key>Privileges</key>
							<integer>509</integer>
							<key>Type</key>
							<integer>1</integer>
							<key>UID</key>
							<integer>0</integer>
						</dict>
						<dict>
							<key>Children</key>
							<array>
								<dict>
									<key>Children</key>
									<array/>
									<key>GID</key>
									<integer>80</integer>
									<key>Path</key>
									<string>Application Support</string>
									<key>Path Type</key>
									<integer>1</integer>
									<key>Privileges</key>
									<integer>509</integer>
									<key>Type</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>Children</key>
									<array/>
									<key>GID</key>
									<integer>80</integer>
									<key>Path</key>
									<string>Documentation</string>
									<key>Path Type</key>
									<integer>1</integer>
									<key>Privileges</key>
									<integer>509</integer>
									<key>Type</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>Children</key>
									<array/>
									<key>GID</key>
									<integer>80</integer>
									<key>Path</key>
									<string>Filesystems</string>
									<key>Path Type</key>
									<integer>1</integer>
									<key>Privileges</key>
									<integer>509</integer>
									<key>Type</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>Children</key>
									<array/>
									<key>GID</key>
									<integer>80</integer>
									<key>Path</key>
									<string>Frameworks</string>
									<key>Path Type</key>
									<integer>1</integer>
									<key>Privileges</key>
									<integer>509</integer>
									<key>Type</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>Children</key>
									<array/>
									<key>GID</key>
									<integer>80</integer>
									<key>Path</key>
									<string>Internet Plug-Ins</string>
									<key>Path Type</key>
									<integer>1</integer>
									<key>Privileges</key>
									<integer>509</integer>
									<key>Type</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>Children</key>
									<array/>
									<key>GID</key>
									<integer>80</integer>
									<key>Path</key>
									<string>PreferencePanes</string>
									<key>Path Type</key>
									<integer>1</integer>
									<key>Privileges</key>
									<integer>509</integer>
									<key>Type</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>Children</key>
									<array/>
									<key>GID</key>
									<integer>80</integer>
									<key>Path</key>
									<string>Preferences</string>
									<key>Path Type</key>
									<integer>1</integer>
									<key>Privileges</key>
									<integer>509</integer>
									<key>Type</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>Children</key>
									<array/>
									<key>GID</key>
									<integer>80</integer>
									<key>Path</key>
									<string>Printers</string>
									<key>Path Type</key>
									<integer>1</integer>
									<key>Privileges</key>
									<integer>509</integer>
									<key>Type</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>Children</key>
									<array/>
									<key>GID</key>
									<integer>80</integer>
									<key>Path</key>
									<string>QuickTime</string>
									<key>Path Type</key>
									<integer>1</integer>
									<key>Privileges</key>
									<integer>509</integer>
									<key>Type</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
								<dict>
									<key>Children</key>
									<array/>
									<key>GID</key>
									<integer>80</integer>
									<key>Path</key>
									<string>Scripts</string>
									<key>Path Type</key>
									<integer>1</integer>
									<key>Privileges</key>
									<integer>509</integer>
									<key>Type</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
							</array>
							<key>GID</key>
							<integer>80</integer>
							<key>Path</key>
							<string>Library</string>
							<key>Path Type</key>
							<integer>1</integer>
							<key>Privileges</key>
							<integer>1021</integer>
							<key>Type</key>
							<integer>1</integer>
							<key>UID</key>
							<integer>0</integer>
						</dict>
						<dict>
							<key>Children</key>
							<array>
								<dict>
									<key>Children</key>
									<array>
										<dict>
											<key>Children</key>
											<array/>
											<key>GID</key>
											<integer>0</integer>
											<key>Path</key>
											<string>Extensions</string>
											<key>Path Type</key>
											<integer>1</integer>
											<key>Privileges</key>
											<integer>493</integer>
											<key>Type</key>
											<integer>1</integer>
											<key>UID</key>
											<integer>0</integer>
										</dict>
									</array>
									<key>GID</key>
									<integer>0</integer>
									<key>Path</key>
									<string>Library</string>
									<key>Path Type</key>
									<integer>1</integer>
									<key>Privileges</key>
									<integer>493</integer>
									<key>Type</key>
									<integer>1</integer>
									<key>UID</key>
									<integer>0</integer>
								</dict>
							</array>
							<key>GID</key>
							<integer>0</integer>
							<key>Path</key>
							<string>System</string>
							<key>Path Type</key>
							<integer>1</integer>
							<key>Privileges</key>
							<integer>493</integer>
							<key>Type</key>
							<integer>1</integer>
							<key>UID</key>
							<integer>0</integer>
						</dict>
					</array>
					<key>GID</key>
					<integer>80</integer>
					<key>Path</key>
					<string>/</string>
					<key>Path Type</key>
					<integer>1</integer>
					<key>Privileges</key>
					<integer>1021</integer>
					<key>Type</key>
					<integer>1</integer>
					<key>UID</key>
					<integer>0</integer>
				</dict>
				<key>IFPkgFlagDefaultLocation</key>
				<string>/Applications</string>
				<key>Imported Package</key>
				<false/>
				<key>Package Path</key>
				<string></string>
				<key>Split Forks</key>
				<true/>
			</dict>
			<key>Plugins</key>
			<dict>
				<key>PluginsList</key>
				<array>
					<dict>
						<key>Path</key>
						<string>Introduction</string>
						<key>Type</key>
						<integer>0</integer>
					</dict>
					<dict>
						<key>Path</key>
						<string>ReadMe</string>
						<key>Type</key>
						<integer>0</integer>
					</dict>
					<dict>
						<key>Path</key>
						<string>License</string>
						<key>Type</key>
						<integer>0</integer>
					</dict>
					<dict>
						<key>Path</key>
						<string>Target</string>
						<key>Type</key>
						<integer>0</integer>
					</dict>
					<dict>
						<key>Path</key>
						<string>PackageSelection</string>
						<key>Type</key>
						<integer>0</integer>
					</dict>
					<dict>
						<key>Path</key>
						<string>Install</string>
						<key>Type</key>
						<integer>0</integer>
					</dict>
					<dict>
						<key>Path</key>
						<string>FinishUp</string>
						<key>Type</key>
						<integer>0</integer>
					</dict>
				</array>
			</dict>
			<key>Scripts</key>
			<dict>
				<key>Additional Resources</key>
				<dict>
					<key>International</key>
					<array/>
				</dict>
				<key>Installation Scripts</key>
				<dict>
					<key>IFInstallationScriptsPostflight</key>
					<dict>
						<key>Path</key>
						<string></string>
						<key>Status</key>
						<false/>
					</dict>
					<key>IFInstallationScriptsPostinstall</key>
					<dict>
						<key>Path</key>
						<string></string>
						<key>Path Type</key>
						<integer>2</integer>
						<key>Status</key>
						<false/>
					</dict>
					<key>IFInstallationScriptsPostupgrade</key>
					<dict>
						<key>Path</key>
						<string></string>
						<key>Status</key>
						<false/>
					</dict>
					<key>IFInstallationScriptsPreflight</key>
					<dict>
						<key>Path</key>
						<string></string>
						<key>Status</key>
						<false/>
					</dict>
					<key>IFInstallationScriptsPreinstall</key>
					<dict>
						<key>Path</key>
						<string></string>
						<key>Status</key>
						<false/>
					</dict>
					<key>IFInstallationScriptsPreupgrade</key>
					<dict>
						<key>Path</key>
						<string></string>
						<key>Status</key>
						<false/>
					</dict>
				</dict>
				<key>Requirements</key>
				<array/>
			</dict>
			<key>Settings</key>
			<dict>
				<key>Description</key>
				<dict>
					<key>International</key>
					<dict>
						<key>IFPkgDescriptionDeleteWarning</key>
						<string></string>
						<key>IFPkgDescriptionDescription</key>
						<string>OpenBoard is an hardware independent interactive whiteboard</string>
						<key>IFPkgDescriptionTitle</key>
						<string>OpenBoard</string>
						<key>IFPkgDescriptionVersion</key>
						<string>1.40.b.00</string>
					</dict>
				</dict>
				<key>Display Information</key>
				<dict>
					<key>CFBundleGetInfoString</key>
					<string>OpenBoard 1.0 Copyrights © 2013 OpenBoard</string>
					<key>CFBundleIconFile</key>
					<string>resources/macx/OpenBoardDmg.icns</string>
					<key>CFBundleIconFile Path Type</key>
					<integer>2</integer>
					<key>CFBundleIdentifier</key>
					<string>org.OpenBoard.pkg.OpenBoard</string>
					<key>CFBundleName</key>
					<string>OpenBoard</string>
					<key>CFBundleShortVersionString</key>
					<string>1.40.b.00</string>
				</dict>
				<key>Options</key>
				<dict>
					<key>IFPkgFlagAllowBackRev</key>
					<false/>
					<key>IFPkgFlagAuthorizationAction</key>
					<integer>0</integer>
					<key>IFPkgFlagFollowLinks</key>
					<false/>
					<key>IFPkgFlagIsRequired</key>
					<false/>
					<key>IFPkgFlagOverwritePermissions</key>
					<false/>
					<key>IFPkgFlagRelocatable</key>
					<false/>
					<key>IFPkgFlagRestartAction</key>
					<integer>0</integer>
					<key>IFPkgFlagRootVolumeOnly</key>
					<false/>
					<key>IFPkgFlagUpdateInstalledLanguages</key>
					<false/>
				</dict>
				<key>Version</key>
				<dict>
					<key>IFMajorVersion</key>
					<integer>1</integer>
					<key>IFMinorVersion</key>
					<integer>40</integer>
				</dict>
			</dict>
		</dict>
		<key>IFPkgFlagPackageSelection</key>
		<integer>0</integer>
		<key>Name</key>
		<string>OpenBoard</string>
		<key>Status</key>
		<integer>1</integer>
		<key>Type</key>
		<integer>1</integer>
	</dict>
	<key>Name</key>
	<string>Project</string>
	<key>Settings</key>
	<dict>
		<key>10.1 Compatibility</key>
		<true/>
		<key>Build Path</key>
		<string>install/mac</string>
		<key>Build Path Type</key>
		<integer>2</integer>
		<key>Comment</key>
		<string></string>
		<key>Remove .DS_Store</key>
		<true/>
		<key>Remove .pbdevelopment</key>
		<true/>
		<key>Remove CVS</key>
		<true/>
	</dict>
</dict>
</plist>
