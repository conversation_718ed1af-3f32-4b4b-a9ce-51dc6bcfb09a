<h3>Magic box</h3
<h4>Assumptions</h4>

<p>The cards pass through a box and are transformed. The goal is to identify the operation done by the box using a minimum of cards.</p>
<p> To move a card through the box, just click on it. </p>

<p>The assumptions may be written on the white board (outside the App).</p>

<p>The "Reload" button replaces cards on the left stack.</p>

<p> Enter the "Edit" mode to choose : </p>

<ul><li> choose the theme of interactivity : pad, slate or none (by default : pad),</li>
<li>set the number of cards that you want to use,</li>
<li>set the cards before and after transformation.</li></ul>
	
<p>The cards have by default a text field. To insert text, click on it. To drag and drop an image from your library, select the checkbox “use image”.</p>	

<p> "Display" button comes back to the activity.</p>