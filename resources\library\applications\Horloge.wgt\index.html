<html>

  <head>
    <title><PERSON><PERSON><PERSON>e</title>

<!-- 
Copyright (c) 2010 Rue<PERSON><PERSON> Appel
ludi at mac dot com
Date: 2010-08-08
Version: 1.0.0 
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at 
http://www.apache.org/licenses/LICENSE-2.0
Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
 * Known Issues : Shadows for some second hands is not on one layer
 -->
 
 
      <script type="text/javascript" src="station-clock.js"></script>
      <script type="text/javascript">

      var clock = new StationClock("clock");
      clock.body = StationClock.RoundBody;
      clock.dial = StationClock.SwissStrokeDial;
      clock.hourHand = StationClock.BarMinuteHand;
      clock.minuteHand = StationClock.SwissMinuteHand;
      clock.secondHand = StationClock.HoleShapedSecondHand;
      clock.boss = StationClock.RedBoss;
      clock.minuteHandBehavoir = StationClock.BouncingMinuteHand;
      clock.secondHandBehavoir = StationClock.BouncingSecondHand;

      animate();

      function animate() {
        clock.draw();
        window.setTimeout("animate()", 50);
      }

    </script>
	
  </head>
  
  <body>

    <div style="text-align: center; width: 100%; height:100%; ">
    <div style="line-height: 0px; ">
    <div style="overflow: hidden; ">
  
      <canvas id="clock" width="400" height="400">
        Horloge       
      </canvas>
    </div>
  </body>
  
</html>
