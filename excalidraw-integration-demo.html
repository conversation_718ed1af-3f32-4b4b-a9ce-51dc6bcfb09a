<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excalidraw + Google Slides Integration Demo</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
        }
        
        .app-container {
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .toolbar {
            background: white;
            border-bottom: 1px solid #e0e0e0;
            padding: 12px 20px;
            display: flex;
            align-items: center;
            gap: 16px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            z-index: 100;
        }
        
        .toolbar h1 {
            margin: 0;
            font-size: 20px;
            color: #333;
            font-weight: 600;
        }
        
        .toolbar-buttons {
            display: flex;
            gap: 12px;
            margin-left: auto;
        }
        
        .btn {
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }
        
        .btn:hover {
            background: #3367d6;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        .btn.secondary {
            background: #34a853;
        }
        
        .btn.secondary:hover {
            background: #2d8f47;
        }
        
        .main-content {
            flex: 1;
            display: flex;
            position: relative;
        }
        
        .excalidraw-panel {
            flex: 1;
            background: white;
            border-right: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 18px;
        }
        
        .slides-panel {
            width: 400px;
            background: #f8f9fa;
            border-left: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
            transition: width 0.3s ease;
        }
        
        .slides-panel.collapsed {
            width: 0;
            overflow: hidden;
        }
        
        .slides-header {
            padding: 16px;
            border-bottom: 1px solid #e0e0e0;
            background: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .slides-content {
            flex: 1;
            padding: 16px;
        }
        
        .url-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 12px;
            box-sizing: border-box;
        }
        
        .url-input:focus {
            outline: none;
            border-color: #4285f4;
            box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
        }
        
        .slide-controls {
            display: flex;
            gap: 8px;
            margin-bottom: 12px;
            flex-wrap: wrap;
        }
        
        .slide-controls .btn {
            font-size: 12px;
            padding: 6px 12px;
        }
        
        .slide-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            color: #666;
            margin-bottom: 12px;
        }
        
        .slide-input {
            width: 50px;
            padding: 4px 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
            font-size: 12px;
        }
        
        .iframe-container {
            width: 100%;
            height: 300px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            transition: all 0.3s ease;
        }


        
        .slides-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .instructions {
            background: #e8f0fe;
            padding: 12px;
            border-radius: 6px;
            font-size: 12px;
            color: #1a73e8;
            line-height: 1.4;
        }
        
        .floating-slides {
            position: fixed;
            top: 80px;
            right: 20px;
            width: 400px;
            height: 500px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            display: none;
            flex-direction: column;
            overflow: hidden;
        }
        
        .floating-slides.visible {
            display: flex;
        }
        
        .floating-header {
            background: #4285f4;
            color: white;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: move;
        }
        
        .floating-content {
            flex: 1;
            padding: 16px;
            overflow-y: auto;
        }
        
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            
            .slides-panel {
                width: 100%;
                height: 300px;
            }
            
            .floating-slides {
                width: 90vw;
                height: 70vh;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                right: auto;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="toolbar">
            <h1>🎨 Excalidraw + Google Slides Integration</h1>
            <div class="toolbar-buttons">
                <button class="btn" onclick="toggleSlidesPanel()">
                    📊 Toggle Slides Panel
                </button>
                <button class="btn secondary" onclick="toggleFloatingSlides()">
                    🔄 Floating Viewer
                </button>
                <button class="btn" onclick="openFullDemo()">
                    🚀 Full Demo
                </button>
            </div>
        </div>
        
        <div class="main-content">
            <div class="excalidraw-panel">
                <div>
                    <h2>🎨 Excalidraw Canvas Area</h2>
                    <p>This is where the Excalidraw whiteboard would be integrated</p>
                    <p>Users can draw, annotate, and collaborate while viewing slides</p>
                </div>
            </div>
            
            <div class="slides-panel" id="slidesPanel">
                <div class="slides-header">
                    <h3>📊 Google Slides</h3>
                    <button class="btn" onclick="toggleSlidesPanel()" style="padding: 4px 8px; font-size: 12px;">×</button>
                </div>
                <div class="slides-content">
                    <div class="instructions">
                        💡 <strong>How to use:</strong><br>
                        1. Open your Google Slides presentation<br>
                        2. Click "Share" → "Anyone with the link can view"<br>
                        3. Copy the URL and paste it below<br>
                        4. The URL should look like: docs.google.com/presentation/d/[ID]/edit
                    </div>
                    
                    <input 
                        type="url" 
                        id="urlInput"
                        class="url-input" 
                        placeholder="Paste Google Slides URL here..."
                    />
                    
                    <div class="slide-controls">
                        <button class="btn" onclick="loadPresentation()">Load</button>
                        <button class="btn" onclick="navigateSlide(-1)" id="prevBtn" disabled>←</button>
                        <button class="btn" onclick="navigateSlide(1)" id="nextBtn" disabled>→</button>
                        <button class="btn secondary" onclick="openPresentationInNewTab()" id="presentBtn" disabled>Present in New Tab</button>
                    </div>
                    
                    <div class="slide-info">
                        <span>Slide</span>
                        <input type="number" id="slideInput" class="slide-input" value="1" min="1" onchange="goToSlide()">
                        <span>of <span id="totalSlides">1</span></span>
                    </div>
                    
                    <div class="iframe-container">
                        <iframe id="slidesFrame" class="slides-iframe" src="about:blank"></iframe>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="floating-slides" id="floatingSlides">
            <div class="floating-header">
                <span>📊 Google Slides Viewer</span>
                <button onclick="toggleFloatingSlides()" style="background: none; border: none; color: white; cursor: pointer;">×</button>
            </div>
            <div class="floating-content">
                <div class="instructions">
                    This floating viewer can be moved around and resized. Perfect for keeping slides visible while working on the whiteboard.
                </div>
                
                <input 
                    type="url" 
                    id="floatingUrlInput"
                    class="url-input" 
                    placeholder="Paste Google Slides URL here..."
                />
                
                <div class="slide-controls">
                    <button class="btn" onclick="loadFloatingPresentation()">Load</button>
                    <button class="btn" onclick="navigateFloatingSlide(-1)" id="floatingPrevBtn" disabled>←</button>
                    <button class="btn" onclick="navigateFloatingSlide(1)" id="floatingNextBtn" disabled>→</button>
                </div>
                
                <div class="iframe-container" style="height: 250px;">
                    <iframe id="floatingSlidesFrame" class="slides-iframe" src="about:blank"></iframe>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Panel state
        let slidesPanelVisible = true;
        let floatingVisible = false;

        // Presentation state
        let currentSlide = 1;
        let totalSlides = 20;
        let presentationId = '';
        let isPresentMode = false;

        // Floating presentation state
        let floatingCurrentSlide = 1;
        let floatingPresentationId = '';

        function extractPresentationId(url) {
            const patterns = [
                /\/presentation\/d\/([a-zA-Z0-9-_]+)/,
                /\/presentation\/d\/([a-zA-Z0-9-_]+)\/edit/,
                /\/presentation\/d\/([a-zA-Z0-9-_]+)\/view/
            ];

            for (const pattern of patterns) {
                const match = url.match(pattern);
                if (match) return match[1];
            }
            return null;
        }

        function generateEmbedUrl(id, slide) {
            slide = slide || 1;
            // Always use the same embed URL - present mode is handled by CSS only
            return 'https://docs.google.com/presentation/d/' + id + '/embed?start=false&loop=false&delayms=3000&slide=' + slide + '&rm=minimal';
        }
        
        function toggleSlidesPanel() {
            const panel = document.getElementById('slidesPanel');
            slidesPanelVisible = !slidesPanelVisible;
            panel.classList.toggle('collapsed', !slidesPanelVisible);
        }

        function toggleFloatingSlides() {
            const floating = document.getElementById('floatingSlides');
            floatingVisible = !floatingVisible;
            floating.classList.toggle('visible', floatingVisible);
        }

        function loadPresentation() {
            const url = document.getElementById('urlInput').value.trim();
            const id = extractPresentationId(url);

            if (!id) {
                alert('Invalid Google Slides URL. Please make sure the presentation is shared publicly.');
                return;
            }

            presentationId = id;
            currentSlide = 1;
            totalSlides = 20;
            updateControls();
            updateSlideDisplay();

            var iframe = document.getElementById('slidesFrame');
            iframe.src = generateEmbedUrl(id, currentSlide);
        }
        
        function loadFloatingPresentation() {
            var url = document.getElementById('floatingUrlInput').value.trim();
            var id = extractPresentationId(url);

            if (!id) {
                alert('Invalid Google Slides URL. Please make sure the presentation is shared publicly.');
                return;
            }

            floatingPresentationId = id;
            floatingCurrentSlide = 1;
            updateFloatingControls();

            var iframe = document.getElementById('floatingSlidesFrame');
            iframe.src = generateEmbedUrl(id, floatingCurrentSlide);
        }

        function openFullDemo() {
            window.open('google-slides-demo.html', '_blank');
        }
        
        function navigateSlide(direction) {
            if (!presentationId) return;

            const newSlide = currentSlide + direction;
            if (newSlide < 1 || newSlide > totalSlides) return;

            currentSlide = newSlide;
            updateSlideDisplay();
            updateControls();

            var iframe = document.getElementById('slidesFrame');
            iframe.src = generateEmbedUrl(presentationId, currentSlide);
        }

        function goToSlide() {
            if (!presentationId) return;

            const slideInput = document.getElementById('slideInput');
            const slide = parseInt(slideInput.value) || 1;

            if (slide < 1 || slide > totalSlides) {
                slideInput.value = currentSlide;
                return;
            }

            currentSlide = slide;
            updateControls();
            updateSlideDisplay();

            var iframe = document.getElementById('slidesFrame');
            iframe.src = generateEmbedUrl(presentationId, currentSlide);
        }

        function openPresentationInNewTab() {
            if (!presentationId) return;

            // Open the original Google Slides presentation in a new tab
            var originalUrl = 'https://docs.google.com/presentation/d/' + presentationId + '/present?start=false&loop=false&delayms=3000&slide=' + currentSlide;
            window.open(originalUrl, '_blank');
        }
        
        function navigateFloatingSlide(direction) {
            if (!floatingPresentationId) return;

            var newSlide = floatingCurrentSlide + direction;
            if (newSlide < 1 || newSlide > totalSlides) return;

            floatingCurrentSlide = newSlide;
            updateFloatingControls();

            var iframe = document.getElementById('floatingSlidesFrame');
            iframe.src = generateEmbedUrl(floatingPresentationId, floatingCurrentSlide);
        }

        function updateSlideDisplay() {
            document.getElementById('slideInput').value = currentSlide;
            document.getElementById('totalSlides').textContent = totalSlides;
        }

        function updateControls() {
            document.getElementById('prevBtn').disabled = currentSlide <= 1 || !presentationId;
            document.getElementById('nextBtn').disabled = currentSlide >= totalSlides || !presentationId;
            document.getElementById('presentBtn').disabled = !presentationId;
        }

        function updateFloatingControls() {
            document.getElementById('floatingPrevBtn').disabled = floatingCurrentSlide <= 1 || !floatingPresentationId;
            document.getElementById('floatingNextBtn').disabled = floatingCurrentSlide >= totalSlides || !floatingPresentationId;
        }
        
        // Initialize when page loads
        window.onload = function() {
            updateControls();
            updateSlideDisplay();
        };

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            if (e.target.tagName === 'INPUT') return;

            switch(e.key) {
                case 'ArrowLeft':
                    if (presentationId) {
                        e.preventDefault();
                        navigateSlide(-1);
                    }
                    break;
                case 'ArrowRight':
                    if (presentationId) {
                        e.preventDefault();
                        navigateSlide(1);
                    }
                    break;
            }
        });
    </script>
</body>
</html>
