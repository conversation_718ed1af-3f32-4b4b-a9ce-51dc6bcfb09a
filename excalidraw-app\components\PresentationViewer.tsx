import React, { useState, useEffect } from 'react';
import './PresentationViewer.scss';

interface PresentationViewerProps {
  isVisible: boolean;
  onClose: () => void;
}

export const PresentationViewer: React.FC<PresentationViewerProps> = ({
  isVisible,
  onClose
}) => {
  const [presentationUrl, setPresentationUrl] = useState('');
  const [embedUrl, setEmbedUrl] = useState('');
  const [currentSlide, setCurrentSlide] = useState(1);
  const [totalSlides, setTotalSlides] = useState(1);
  const [viewMode, setViewMode] = useState<'embed' | 'present'>('embed');

  // Extract presentation ID from Google Slides URL
  const extractPresentationId = (url: string): string | null => {
    const patterns = [
      /\/presentation\/d\/([a-zA-Z0-9-_]+)/,
      /\/presentation\/d\/([a-zA-Z0-9-_]+)\/edit/,
      /\/presentation\/d\/([a-zA-Z0-9-_]+)\/view/
    ];
    
    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) return match[1];
    }
    return null;
  };

  // Generate embed URL for Google Slides
  const generateEmbedUrl = (url: string, slide: number = 1): string => {
    const presentationId = extractPresentationId(url);
    if (!presentationId) return '';
    
    if (viewMode === 'present') {
      return `https://docs.google.com/presentation/d/${presentationId}/present?start=false&loop=false&delayms=3000&slide=${slide}`;
    } else {
      return `https://docs.google.com/presentation/d/${presentationId}/embed?start=false&loop=false&delayms=3000&slide=${slide}`;
    }
  };

  // Handle URL input change
  const handleUrlChange = (url: string) => {
    setPresentationUrl(url);
    if (url && extractPresentationId(url)) {
      setEmbedUrl(generateEmbedUrl(url, currentSlide));
    } else {
      setEmbedUrl('');
    }
  };

  // Navigate slides
  const navigateSlide = (direction: number) => {
    const newSlide = Math.max(1, Math.min(totalSlides, currentSlide + direction));
    setCurrentSlide(newSlide);
    if (presentationUrl) {
      setEmbedUrl(generateEmbedUrl(presentationUrl, newSlide));
    }
  };

  // Toggle view mode
  const toggleViewMode = () => {
    const newMode = viewMode === 'embed' ? 'present' : 'embed';
    setViewMode(newMode);
    if (presentationUrl) {
      setEmbedUrl(generateEmbedUrl(presentationUrl, currentSlide));
    }
  };

  if (!isVisible) return null;

  return (
    <div className="presentation-viewer-overlay">
      <div className="presentation-viewer">
        <div className="presentation-header">
          <div className="header-left">
            <h3>📊 Google Slides Viewer</h3>
          </div>
          <div className="header-controls">
            <button 
              className="view-mode-btn"
              onClick={toggleViewMode}
              title={`Switch to ${viewMode === 'embed' ? 'presentation' : 'embed'} mode`}
            >
              {viewMode === 'embed' ? '🖥️' : '📋'}
            </button>
            <button className="close-btn" onClick={onClose}>✕</button>
          </div>
        </div>

        <div className="url-input-section">
          <input
            type="url"
            placeholder="Paste Google Slides URL here (e.g., https://docs.google.com/presentation/d/...)"
            value={presentationUrl}
            onChange={(e) => handleUrlChange(e.target.value)}
            className="url-input"
          />
          <div className="url-help">
            💡 Tip: Copy the URL from your Google Slides presentation and paste it above
          </div>
        </div>

        {embedUrl && (
          <>
            <div className="slide-controls">
              <button 
                onClick={() => navigateSlide(-1)}
                disabled={currentSlide <= 1}
                className="nav-btn"
              >
                ← Previous
              </button>
              
              <div className="slide-info">
                <input
                  type="number"
                  min="1"
                  max={totalSlides}
                  value={currentSlide}
                  onChange={(e) => {
                    const slide = parseInt(e.target.value) || 1;
                    setCurrentSlide(slide);
                    setEmbedUrl(generateEmbedUrl(presentationUrl, slide));
                  }}
                  className="slide-input"
                />
                <span>of {totalSlides}</span>
              </div>

              <button 
                onClick={() => navigateSlide(1)}
                disabled={currentSlide >= totalSlides}
                className="nav-btn"
              >
                Next →
              </button>

              <div className="slide-controls-right">
                <input
                  type="range"
                  min="1"
                  max={totalSlides}
                  value={currentSlide}
                  onChange={(e) => {
                    const slide = parseInt(e.target.value);
                    setCurrentSlide(slide);
                    setEmbedUrl(generateEmbedUrl(presentationUrl, slide));
                  }}
                  className="slide-slider"
                />
              </div>
            </div>

            <div className="iframe-container">
              <iframe
                src={embedUrl}
                width="100%"
                height="100%"
                frameBorder="0"
                allowFullScreen
                className="slides-iframe"
                title="Google Slides Presentation"
              />
            </div>
          </>
        )}

        {!embedUrl && presentationUrl && (
          <div className="error-message">
            ⚠️ Invalid Google Slides URL. Please make sure the URL is from Google Slides and is publicly accessible.
          </div>
        )}

        {!presentationUrl && (
          <div className="placeholder">
            <div className="placeholder-content">
              <h4>🎯 How to use Google Slides Viewer:</h4>
              <ol>
                <li>Open your Google Slides presentation</li>
                <li>Click "Share" and make sure it's viewable by anyone with the link</li>
                <li>Copy the URL from your browser</li>
                <li>Paste it in the input field above</li>
                <li>Navigate through slides and annotate on the whiteboard!</li>
              </ol>
              
              <div className="example-url">
                <strong>Example URL:</strong><br/>
                <code>https://docs.google.com/presentation/d/1ABC123.../edit</code>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
