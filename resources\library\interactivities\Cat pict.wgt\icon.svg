<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 15.1.0, SVG Export Plug-In  -->
<!DOCTYPE svg [
	<!ENTITY ns_flows "http://ns.adobe.com/Flows/1.0/">
]>
<svg version="1.2" baseProfile="tiny"
	 xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:a="http://ns.adobe.com/AdobeSVGViewerExtensions/3.0/"
	 x="0px" y="0px" width="107px" height="107px" viewBox="0 0 107 107" overflow="visible" xml:space="preserve">
<defs>
</defs>
<g>
	<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="53.6655" y1="0" x2="53.6655" y2="107.3335">
		<stop  offset="0" style="stop-color:#727270"/>
		<stop  offset="1" style="stop-color:#41413F"/>
		<a:midPointStop  offset="0" style="stop-color:#727270"/>
		<a:midPointStop  offset="0.5" style="stop-color:#727270"/>
		<a:midPointStop  offset="1" style="stop-color:#41413F"/>
	</linearGradient>
	<path fill="url(#SVGID_1_)" d="M107.3,99.3c0,4.4-3.6,8-8,8H8c-4.4,0-8-3.6-8-8V8c0-4.4,3.6-8,8-8h91.3c4.4,0,8,3.6,8,8V99.3z"/>
</g>
<g>
	<rect x="10.4" y="69.9" fill-rule="evenodd" fill="#FFFFFF" width="28.5" height="23.2"/>
	<g id="PfcyK0_2_">
		<g>
			<g>
				<path fill-rule="evenodd" fill="#9CD1EA" d="M37.3,77.3c-5.2-0.6-9.8,0.9-15,1c-0.2,0-0.4,0-0.6,0c-2.6-0.4-4.9-1-7.8-1.1
					c-0.5-0.1-1.2,0-1.8-0.1c0-1.8,0-3.7,0-5.5c8.4,0,16.8,0,25.2,0C37.3,73.5,37.3,75.4,37.3,77.3z"/>
				<path fill-rule="evenodd" fill="#70B0D0" d="M12.1,77.2C12.1,77.1,12.1,77.1,12.1,77.2c0.6,0,1.3-0.1,1.8,0
					C13.3,77.2,12.7,77.2,12.1,77.2z"/>
				<path fill-rule="evenodd" fill="#70B0D0" d="M21.7,78.3c0.2,0,0.4,0,0.6,0C22.2,78.4,21.7,78.4,21.7,78.3z"/>
				<path fill-rule="evenodd" fill="#9CD1EA" d="M23.7,80.8c4.5,0,8.9,0,13.6,0c0,0.9,0,1.8,0,2.8c-0.4-0.1-0.8,0-1.2-0.3
					c-0.3,0.1-0.4,0.3-0.9,0.2c-0.2-0.1-0.6-0.1-0.8,0c-0.7,0.2-1.7,0-2.2-0.3c-0.2-0.1-0.1,0.2-0.3,0.2c-1.6-0.3-3-0.8-4.9-0.8
					c-0.1-0.1-0.5,0-0.7-0.1c-2.3-0.2-4.7-0.4-6.5-1.1c-0.5,0-0.9-0.2-1.5-0.2c-0.1-0.1-0.5,0-0.7,0c-0.9,0.1-1.5-0.1-2.1-0.3
					c0.5-0.1,1.3,0.1,1.9,0c0.2,0.1,0.8,0.1,1.1,0c0.2,0,0.2,0,0.4-0.1c0.2,0,0.4,0,0.6,0c0.1,0,0.1,0,0.2,0c0.4,0.1,1.1,0.1,1.5,0
					c0.4,0,0.8-0.1,1.2-0.1c0.2,0,0.6,0.1,0.8,0c0.1,0,0.1,0,0.2,0C23.3,80.8,23.6,80.9,23.7,80.8z"/>
				<path fill-rule="evenodd" fill="#70B0D0" d="M23.1,80.8c0.1-0.1,0.4,0,0.6-0.1C23.6,80.9,23.3,80.8,23.1,80.8z"/>
				<path fill-rule="evenodd" fill="#70B0D0" d="M22.2,80.9c0.2-0.1,0.5,0,0.8,0C22.8,81,22.4,80.9,22.2,80.9z"/>
				<path fill-rule="evenodd" fill="#70B0D0" d="M18.7,81c0.1-0.1,0.5-0.1,0.6,0C19.1,81,18.9,81,18.7,81z"/>
				<path fill-rule="evenodd" fill="#70B0D0" d="M19.5,81c0.5,0,1,0,1.5,0C20.6,81.1,19.9,81.1,19.5,81z"/>
				<path fill-rule="evenodd" fill="#70B0D0" d="M17.3,81c0.4,0,0.7,0,1.1,0C18.1,81.1,17.6,81.1,17.3,81z"/>
				<path fill-rule="evenodd" fill="#70B0D0" d="M17.5,81.3c0.2,0,0.6-0.1,0.7,0C18,81.3,17.7,81.4,17.5,81.3z"/>
				<path fill-rule="evenodd" fill="#70B0D0" d="M26.3,82.5c0.2,0,0.6-0.1,0.7,0.1C26.8,82.6,26.4,82.7,26.3,82.5z"/>
				<path fill-rule="evenodd" fill="#5A9FC1" d="M37.3,87.3c0,0.2,0,0.3,0,0.5c-0.9,0.4-2,0.4-3.2,0.5C35,87.8,36,87.4,37.3,87.3z"
					/>
				<path fill-rule="evenodd" fill="#5A9FC1" d="M18.2,81.3c0.6,0,1,0.1,1.5,0.2c1.9,0.7,4.2,0.8,6.5,1.1c0.1,0.1,0.5,0,0.7,0.1
					c1.9,0,3.4,0.4,4.9,0.8c0.2,0.1,0.1-0.2,0.3-0.2c0.6,0.3,1.5,0.5,2.2,0.3c0.1,0.1,0.5,0,0.8,0c0.5,0.1,0.6,0,0.9-0.2
					c0.4,0.3,0.8,0.1,1.2,0.3c0,1.2,0,2.4,0,3.5c-0.5-0.1-1.1,0.3-1.5,0.2c0,0,0-0.1,0-0.1c-0.3,0-0.5,0.2-0.7,0.2
					c-0.1,0-0.2-0.1-0.3-0.1c-0.3,0-0.8,0.3-1.3,0.3c-0.2,0,0.2-0.2,0-0.4c-0.5,0.2-1.4,0.5-1.8,0.1c-1.5,0.4-3.3,0.7-5.1,0.6
					c1.1-0.4,2.4-0.4,3.5-0.7c-3.4-0.2-5.8,0.6-9,0.8c0.1-0.3,0.5-0.2,0.7-0.2c1.6-0.2,3.4-0.6,4.7-1.2c-1.3,0-2.6,0.6-4,0.7
					c-0.8,0.1-1.5,0-2.2,0.3c0.9-0.8,2.8-0.8,3.8-1.5c-0.4-0.2-0.9-0.1-1.3,0c-0.2,0,0.1-0.1,0.1-0.2c-0.7,0-1.3,0-1.9,0.1
					c-0.1-0.1-0.3-0.1-0.3-0.3c-1.1,0.2-1.9-0.2-3,0c0-0.1,0.3-0.1,0.5-0.2c0.2-0.1,0.2-0.2,0.4-0.3c-1.1-0.3-2.5,0.1-3-0.7
					c-1.6,0.2-1.9-1.2-3.3-1.3c0-0.1,0-0.1-0.1-0.1c0-2,0-4,0-5.9c0.6,0,1.2,0,1.8,0c2.9,0.1,5.2,0.7,7.8,1.1c0.1,0.1,0.5,0.1,0.6,0
					c5.2,0,9.8-1.5,15-1c0,1.2,0,2.3,0,3.5c-4.6,0-9,0-13.6,0c-0.2,0-0.5-0.1-0.6,0.1c-0.1,0-0.1,0-0.2,0c-0.2,0-0.6-0.1-0.8,0
					c-0.4,0-0.8,0.1-1.2,0.1c-0.5,0-1,0-1.5,0c-0.1,0-0.1,0-0.2,0c-0.1-0.1-0.5-0.1-0.6,0c-0.1,0-0.2,0.1-0.4,0.1
					c-0.4,0-0.7,0-1.1,0c-0.5,0.1-1.3-0.1-1.9,0c0.6,0.2,1.2,0.3,2.1,0.3C17.7,81.4,18,81.3,18.2,81.3z"/>
				<path fill-rule="evenodd" fill="#70B0D0" d="M34.5,83.5c0.2-0.1,0.6-0.1,0.8,0C35,83.4,34.6,83.5,34.5,83.5z"/>
				<path fill-rule="evenodd" fill="#70B0D0" d="M12.1,83.1c0,0,0.1,0,0.1,0.1c1.4,0.1,1.8,1.5,3.3,1.3c0.6,0.8,1.9,0.4,3,0.7
					c-0.2,0.1-0.2,0.2-0.4,0.3c-0.2,0.1-0.4,0-0.5,0.2c1.1-0.2,1.9,0.2,3,0c0,0.2,0.2,0.2,0.3,0.3c0.6-0.1,1.2-0.1,1.9-0.1
					c0,0.1-0.3,0.2-0.1,0.2c0.3-0.1,0.9-0.2,1.3,0c-1,0.8-2.9,0.7-3.8,1.5c0.7-0.3,1.5-0.2,2.2-0.3c1.4-0.1,2.8-0.7,4-0.7
					c-1.3,0.6-3.1,1-4.7,1.2c-0.2,0-0.6,0-0.7,0.2c3.2-0.1,5.7-1,9-0.8c-1.1,0.3-2.5,0.4-3.5,0.7c1.9,0.2,3.7-0.1,5.1-0.6
					c0.4,0.4,1.3,0.1,1.8-0.1c0.2,0.2-0.2,0.4,0,0.4c0.5,0,0.9-0.3,1.3-0.3c0.1,0,0.2,0.1,0.3,0.1c0.2,0,0.4-0.2,0.7-0.2
					c0,0,0,0.1,0,0.1c0.4,0.1,1-0.3,1.5-0.2c0,0.1,0,0.1,0,0.2c-1.2,0.2-2.3,0.5-3.2,1c1.2-0.1,2.3-0.1,3.2-0.5c0,1.2,0,2.4,0,3.6
					c-8.4,0-16.8,0-25.2,0C12.1,88.6,12.1,85.8,12.1,83.1z"/>
			</g>
		</g>
	</g>
</g>
<g>
	<rect x="43" y="69.9" fill-rule="evenodd" fill="#FFFFFF" width="28.5" height="23.2"/>
	<g id="tykqki_2_">
		<g>
			<g>
				<path fill-rule="evenodd" fill="#BDCF31" d="M60.2,74.4c0.2,0,0.4-0.1,0.5,0.1C60.5,74.4,60.3,74.5,60.2,74.4z"/>
				<path fill-rule="evenodd" fill="#D9D931" d="M64.5,78.1c0.2,0,0.1,0.3,0.1,0.4C64.4,78.5,64.4,78.2,64.5,78.1z"/>
				<path fill-rule="evenodd" fill="#D9D931" d="M65.5,79.6c0,0.1-0.1,0.1-0.2,0.2c-0.1,0-0.2-0.1-0.2-0.2
					C65.2,79.5,65.5,79.5,65.5,79.6z"/>
				<path fill-rule="evenodd" fill="#BDCF31" d="M53.7,81.5c-0.1,0.5,0.2,0.7,0.2,1.1c0.5,0.1,0.8,0.3,0.8,0.7
					c0,0.4-0.6,0.4-0.4,0.8c0,0.1,0.1-0.1,0.3,0c0.3,0.5,1.3,0.3,2,0.4c-0.1,0.1-0.3,0.1-0.3,0.4c0.4,0,1.1-0.2,1.8-0.2
					c0.4-0.4,1.2-0.2,1.6-0.1c-0.1,0.4-0.1,1.2,0,1.6c-0.1,0.1-0.5,0-0.7,0.1c-0.5,1-2.1,0.2-2.7-0.2c0.1,0.4,0.6,0.4,0.9,0.6
					C56.3,87,55.4,86.5,55,86c-0.2,0,0,0.3-0.2,0.3c0.2,0.1,0.3,0.2,0.5,0.4c-0.3,0.1-0.7,0-1-0.1c-0.1,0.1,0.2,0.1,0.1,0.3
					c-0.4,0-0.7-0.2-0.9-0.4c-0.1,0.1,0.2,0.2,0.1,0.5c-0.4-0.1-0.6-0.3-1-0.5c-0.4,0.2-0.7,0.1-1.2,0c0.1,0.1,0.3,0.1,0.4,0.2
					c-0.5,0.2-0.9-0.1-1.3,0c-0.7,0.1-1.3,0.8-1.9,0.4c0.2-0.4,0.8-0.4,1.2-0.6c0-0.2-0.3-0.1-0.3-0.3c-0.1-0.2,0.2-0.1,0.3-0.1
					c0-0.2-0.2-0.2-0.2-0.4c-0.4,0.1-0.5-0.4-0.7-0.6c-0.8,0-1.1,0.3-1.4,0.8c0,0.1,0.1,0.1,0.2,0.2c-0.4,0-0.5,0.2-0.6,0.6
					c0.1,0.2,0.4,0.1,0.6,0.2c0,0.2-0.2,0.1-0.3,0.1c0,0.1,0.1,0.1,0.2,0.2c0.2-0.1,0.5,0,0.9,0.2c-0.6,0.9-2,0.4-3,0.2
					c-0.3,0-0.5,0.1-0.8,0.2c0-1.8,0-3.5,0-5.3c3.3,0.2,5.7-0.6,9-0.4C53.8,81.9,53.6,81.5,53.7,81.5z"/>
				<path fill-rule="evenodd" fill="#9DAD3A" d="M63.7,85.3c0.3,0,0.4,0.2,0.7,0.2C64.2,85.5,63.7,85.5,63.7,85.3z"/>
				<path fill-rule="evenodd" fill="#BDCF31" d="M66,79.1c0.3-0.1,0.6,0,0.7,0.3c-0.1-0.2-0.4-0.1-0.5-0.3
					C66.1,79.1,66.1,79.2,66,79.1C66,79.1,66,79.1,66,79.1z"/>
				<path fill-rule="evenodd" fill="#BDCF31" d="M63.1,75.1c0,0.2,0.2,0.2,0.2,0.4c-0.2-0.1-0.3-0.2-0.5-0.4c0.2,0,0.2,0.2,0.3,0.2
					C63,75.2,63,74.9,63.1,75.1z"/>
				<path fill-rule="evenodd" fill="#BDCF31" d="M59,74c0.3-0.2,0.4,0.2,0.6,0.4C59.3,74.3,59.3,74,59,74z"/>
				<path fill-rule="evenodd" fill="#BDCF31" d="M55.2,77.3c0,0.2-0.2,0.4-0.3,0.5C55,77.6,55.1,77.5,55.2,77.3z"/>
				<path fill-rule="evenodd" fill="#BDCF31" d="M55.2,78.6c-0.1,0.3,0.2,0.6-0.1,0.7C55.4,79.1,55,78.8,55.2,78.6z"/>
				<path fill-rule="evenodd" fill="#9DAD3A" d="M66.8,80.8c0,0.5-0.4,0.6-0.2,1.1c-0.3,0.2-0.4,0.7-0.1,1c-0.1,0-0.1,0-0.2,0
					c0.1,0.6-0.7,0.6-0.4,1.1c0.3,0,0.4-0.4,0.6-0.2c-0.5,0.5-1.7,0.2-2.6,0.3c-0.4,1.1-2,0.1-2.8,0.3c-0.4,0.5-0.3,1.9,0.5,1.9
					c0.4-0.3,1.5,0.1,1.7-0.5c0.5,0,1.1,0.3,1.6,0.1c0-0.1,0-0.2-0.1-0.3c0.3-0.1,0.9,0.5,1.1,0c0.8,0.1,1.5,0.5,2.3,0.5
					c0.2-0.1,0.2-0.3,0.1-0.4c0.3,0,0.6,0.1,0.9,0c0.1-0.1-0.2-0.1-0.3-0.2c0.2,0,0.6,0.2,1.1,0.2c0,2.3,0,3.3,0,5.6
					c-8.4,0-16.8,0-25.2,0c0-1.7,0-2.1,0-3.8c0.3,0,0.5-0.1,0.8-0.2c1,0.2,2.4,0.7,3-0.2c-0.3-0.1-0.7-0.3-0.9-0.2
					c0-0.1-0.2-0.1-0.2-0.2c0.1,0,0.3,0.1,0.3-0.1c-0.2-0.1-0.5,0-0.6-0.2c0-0.3,0.2-0.5,0.6-0.6c-0.1-0.1-0.2-0.1-0.2-0.2
					c0.2-0.5,0.6-0.8,1.4-0.8c0.2,0.2,0.3,0.7,0.7,0.6c0,0.2,0.2,0.2,0.2,0.4c-0.1,0-0.3-0.1-0.3,0.1c0,0.2,0.3,0.1,0.3,0.3
					c-0.4,0.2-1,0.2-1.2,0.6c0.6,0.4,1.2-0.3,1.9-0.4c0.4-0.1,0.8,0.2,1.3,0c-0.1-0.1-0.3-0.1-0.4-0.2c0.5,0.1,0.9,0.2,1.2,0
					c0.4,0.2,0.6,0.4,1,0.5c0.1-0.2-0.2-0.3-0.1-0.5c0.2,0.2,0.5,0.4,0.9,0.4c0.2-0.1-0.2-0.2-0.1-0.3c0.3,0,0.7,0.2,1,0.1
					c-0.2-0.1-0.3-0.3-0.5-0.4c0.2,0,0-0.3,0.2-0.3c0.4,0.5,1.2,1,2.1,0.7c-0.3-0.2-0.8-0.2-0.9-0.6c0.6,0.4,2.1,1.2,2.7,0.2
					c0.2,0,0.6,0.1,0.7-0.1c-0.1-0.4-0.1-1.1,0-1.6c-0.4-0.1-1.2-0.3-1.6,0.1c-0.6,0-1.3,0.2-1.8,0.2c0-0.2,0.2-0.2,0.3-0.4
					c-0.7-0.1-1.7,0.1-2-0.4c-0.1-0.1-0.2,0.1-0.3,0c-0.2-0.4,0.3-0.5,0.4-0.8c0-0.4-0.3-0.6-0.8-0.7c0-0.4-0.2-0.6-0.2-1.1
					c0-0.3,0-0.5,0-0.8c1,0,0.2-1.1,0.6-1.5c0.4,0,0.5,0.3,0.9,0.2c0.3-0.2,0-0.5,0.1-0.7c0-0.3-0.2-0.5-0.4-0.7c0,0,0-0.1,0-0.1
					c0.1-0.1,0.3-0.3,0.3-0.5c0.5-0.8,0.8-1.7,1.6-2.1c0.2,0.2,0.8,0.3,1.1,0.1c0-0.2-0.2-0.1-0.1-0.4c0.2-0.1,0.5-0.2,0.3-0.5
					c0.3-0.2,0.5-0.4,0.9-0.4c0.3,0,0.3,0.3,0.6,0.4c0.3,0,0.3,0.3,0.4,0.4c0.3,0,0.1-0.2,0.1-0.4c0.1-0.1,0.1,0,0.2,0
					c0.1,0.1,0.4,0,0.5,0.1c0.1,0.2,0.3,0,0.6,0.2c0.1-0.3,0.4-0.4,0.9-0.3c-0.2,0.5,0.4,0.6,0.7,0.8c0.1,0.1,0.3,0.3,0.5,0.4
					c0-0.2-0.2-0.2-0.2-0.4c0.4,0.2,0.7,0.5,1,0.9c0.2,0.1,0.1-0.1,0.3-0.1c0,0.2-0.1,0.3-0.1,0.5c0.7-0.2,1.1,0.6,1.5,1
					c-0.2,0-0.1,0.3-0.3,0.2c0.1,0.6,0.4,0.9,0.7,1.3c-0.2,0-0.7,0.2-0.4,0.5c0.2,0,0-0.3,0.3-0.2c0,0,0,0,0,0.1
					c0.1,0,0.1-0.1,0.3-0.1c0.1,0.2,0.4,0.1,0.5,0.3C67.1,79.7,67,80.3,66.8,80.8z M64.5,78.5c0.1-0.1,0.2-0.4-0.1-0.4
					C64.4,78.2,64.4,78.5,64.5,78.5z M65.2,79.6c0,0.1,0.1,0.2,0.2,0.2c0-0.1,0.1-0.1,0.2-0.2C65.5,79.5,65.2,79.5,65.2,79.6z"/>
				<path fill-rule="evenodd" fill="#BDCF31" d="M69.8,80.6c0,1.7,0,3.4,0,5.1c-0.4,0-0.8-0.2-1.1-0.2c0.1,0.1,0.4,0.1,0.3,0.2
					c-0.3,0.1-0.5,0-0.9,0c0.1,0.2,0.1,0.3-0.1,0.4c-0.8,0-1.5-0.4-2.3-0.5c-0.2,0.5-0.8-0.1-1.1,0c0.1,0,0.1,0.1,0.1,0.3
					c-0.5,0.2-1-0.1-1.6-0.1c-0.2,0.5-1.3,0.1-1.7,0.5c-0.7-0.1-0.9-1.4-0.5-1.9c0.8-0.1,2.4,0.8,2.8-0.3c0.9-0.1,2.1,0.3,2.6-0.3
					C66.3,83.5,66.3,84,66,84c-0.4-0.5,0.5-0.5,0.4-1.1c0.1,0,0.1,0,0.2,0c-0.3-0.3-0.2-0.8,0.1-1c-0.2-0.4,0.1-0.5,0.2-1.1
					C67.8,80.9,68.8,80.6,69.8,80.6z M64.4,85.5c-0.3,0-0.4-0.2-0.7-0.2C63.7,85.5,64.2,85.5,64.4,85.5z"/>
				<path fill-rule="evenodd" fill="#D9D931" d="M69.8,80.6c-1,0-2,0.4-3,0.2c0.2-0.5,0.3-1.1-0.1-1.4c-0.1-0.2-0.4-0.3-0.7-0.3
					c-0.2-0.1-0.1,0.2-0.3,0.2c-0.3-0.3,0.2-0.5,0.4-0.5c-0.3-0.3-0.6-0.7-0.7-1.3c0.2,0.1,0.1-0.2,0.3-0.2c-0.4-0.4-0.8-1.2-1.5-1
					c0-0.2,0.1-0.3,0.1-0.5c-0.2,0-0.1,0.2-0.3,0.1c-0.2-0.4-0.6-0.7-1-0.9c-0.1-0.1,0,0.1,0,0.2c-0.1,0-0.1-0.2-0.3-0.2
					c-0.3-0.2-0.9-0.3-0.7-0.8c-0.4-0.1-0.7,0-0.9,0.3c-0.2-0.2-0.5,0.1-0.6-0.2c-0.1-0.1-0.4,0-0.5-0.1c-0.1,0-0.1-0.1-0.2,0
					c0,0.2,0.2,0.4-0.1,0.4c-0.1-0.1-0.1-0.4-0.4-0.4c-0.1-0.1-0.3-0.5-0.6-0.4c-0.4,0-0.7,0.2-0.9,0.4c0.2,0.3-0.1,0.4-0.3,0.5
					c-0.1,0.2,0.1,0.2,0.1,0.4c-0.3,0.2-0.8,0.2-1.1-0.1c-0.8,0.4-1.1,1.3-1.6,2.1c-0.1,0.2-0.2,0.3-0.3,0.5c0,0,0,0.1,0,0.1
					c0.2,0.2,0.3,0.4,0.4,0.7c-0.2,0.2,0.2,0.5-0.1,0.7c-0.3,0.1-0.5-0.2-0.9-0.2c-0.3,0.4,0.4,1.5-0.6,1.5c0,0.3,0,0.5,0,0.8
					c-0.1,0,0,0.3-0.1,0.3c-3.3-0.2-5.7,0.6-9,0.4c0-4,0-6.7,0-10.7c8.4,0,16.8,0,25.2,0C69.8,75,69.8,77.1,69.8,80.6z"/>
			</g>
		</g>
	</g>
</g>
<rect x="71.5" y="63.4" fill-rule="evenodd" fill="#33342E" width="26.3" height="23.2"/>
<path fill-rule="evenodd" fill="#33342E" d="M88.6,80.1c0.1,0.1,0.1,0.5,0,0.6C88.6,80.5,88.6,80.3,88.6,80.1z"/>
<path fill-rule="evenodd" fill="#33342E" d="M71,65.1c8.4,0,16.8,0,25.2,0c0,6.6,0,13.2,0,19.8c-8.4,0-16.8,0-25.2,0
	c0-6.5,0-13.1,0-19.6C70.9,65.1,70.9,65.1,71,65.1z M89.3,80.8c0.1-0.5,0.1-1.1-0.1-1.5c0.2,0.1,0.7-0.1,0.8-0.1
	c0,0.1-0.1,0.2-0.1,0.4c1.3-1,0.3-3.7-0.5-4.5c0-0.2,0.1-0.2,0.1-0.4c-0.8-1.4-3.2-1.6-4.4-2.5c-1.4-1-2.4-1.8-3.5-2.9
	c-0.2-0.3-0.4-0.5-0.7-0.7c-0.2-0.1-0.3-0.3-0.5-0.4c-0.4-0.3-0.8-0.8-0.7-1.4c-0.2,0-0.2,0.2-0.5,0.2c0-0.1,0-0.3-0.1-0.4
	c-0.3,0.9-0.9,1.5-1.4,2.3c0.5,1.1,1-0.1,1.5-0.2c0.3,0,0.8,0.7,1,1.1c0.6,1,1,1.7,1.6,2.6c0.2,0.3,0.8,0.9,0.9,1.3
	c0.1,0.5-0.1,1.1-0.1,1.6c0.2,1,0.7,1.9,0.8,2.8c0,0.6-0.3,1.2-0.1,1.8c-0.1,0.1,0,0.4-0.1,0.6c0,0.1,0,0.1,0,0.2
	c-0.1,0.1,0,0.5,0,0.7c0.1,0.5-0.2,0.6-0.3,1c0.1,0.1,0.4,0,0.6,0c0.1-0.4,0.3-0.8,0.3-1.3c0.1-0.2,0.1-0.7,0-0.8
	c0-0.5,0.1-0.9,0.3-1.2c0.1,0.5,0.1,1.1,0.3,1.6c0,0.4,0,0.9,0,1.3c0,0.3-0.3,0.3-0.4,0.6c0.2,0.1,0.4,0.1,0.7,0.1
	c0.1-0.3,0.2-0.6,0.3-1c0.1-0.1,0.1-0.5,0-0.5c-0.1-1,0.2-2.4-0.2-3.3c0.1-0.3,0.3-0.6,0.4-0.9c0.1-0.2,0.8,0,1-0.2
	c0.4,0,0.7-0.1,1-0.3c0.8,0.7,0.9,2.2,1.3,3.3c-0.1,0.1,0,0.5-0.1,0.7c0,0.7-0.2,1.2-0.4,1.8c0,0,0.1,0,0.1,0c0.2,0,0.5,0.1,0.5-0.1
	c0,0,0-0.1,0-0.1c0.1-0.4,0.1-1,0.1-1.5c0.1-0.1,0.1-0.5,0-0.6c0-0.3-0.1-0.8,0.1-1.1c0.1,0.5,0.2,1,0.3,1.7c0,0.2-0.1,0.4,0.1,0.5
	c0.2,0.6-0.1,0.9-0.1,1.4c0.7,0.2,0.6-0.7,0.5-1.3C89.3,81.2,89.4,80.9,89.3,80.8z"/>
<path fill-rule="evenodd" fill="#33342E" d="M80.8,68.6c0.3,0.2,0.5,0.5,0.7,0.7c-0.2-0.1-0.3-0.2-0.5-0.4
	C81,68.8,80.9,68.8,80.8,68.6z"/>
<path fill-rule="evenodd" fill="#33342E" d="M80.4,68.2c0.2,0.1,0.3,0.3,0.5,0.4c0.1,0.1,0.2,0.2,0.3,0.3
	C80.8,68.7,80.6,68.5,80.4,68.2z"/>
<path fill-rule="evenodd" fill="#33342E" d="M81.5,69.3c1.1,1,2.1,1.9,3.5,2.9c1.2,0.9,3.7,1.1,4.4,2.5c0,0.2-0.1,0.2-0.1,0.4
	c0.8,0.8,1.8,3.5,0.5,4.5c0-0.2,0.1-0.3,0.1-0.4c-0.1,0-0.5,0.2-0.8,0.1c0.1,0.4,0.1,1.1,0.1,1.5c0,0.2-0.1,0.4,0.1,0.5
	c0.1,0.5,0.2,1.5-0.5,1.3c0-0.4,0.3-0.7,0.1-1.4c0-0.2,0.1-0.4-0.1-0.5c0-0.6-0.2-1.1-0.3-1.7c-0.1,0.3,0,0.7-0.1,1.1
	c0,0.2,0,0.4,0,0.6c0,0.5,0,1-0.1,1.5c0,0,0,0,0,0.1c-0.1,0.1-0.4,0-0.5,0.1c0,0-0.1,0-0.1,0c0.2-0.5,0.4-1.1,0.4-1.8
	c0.1-0.1,0-0.5,0.1-0.7c-0.4-1.1-0.5-2.6-1.3-3.3c-0.3,0.1-0.6,0.2-1,0.3c-0.3,0.1-0.9-0.2-1,0.2c-0.1,0.3-0.2,0.6-0.4,0.9
	c0.4,0.9,0.1,2.3,0.2,3.3c0,0.2,0,0.3,0,0.5c-0.1,0.3-0.1,0.7-0.3,1c-0.3,0-0.5,0-0.7-0.1c0.1-0.3,0.4-0.3,0.4-0.6
	c0.1-0.3,0.1-1,0-1.3c-0.1-0.5-0.1-1.1-0.3-1.6c-0.2,0.3-0.3,0.7-0.3,1.2c0,0.3,0,0.5,0,0.8c0,0.5-0.1,0.9-0.3,1.3
	c-0.2,0-0.4,0-0.6,0c0-0.4,0.3-0.5,0.3-1c0.1-0.1,0-0.5,0-0.7c0-0.1,0-0.1,0-0.2c0.1-0.1,0-0.4,0.1-0.6c-0.2-0.6,0.1-1.2,0.1-1.8
	c0-0.9-0.6-1.8-0.8-2.8c-0.1-0.5,0.1-1.1,0.1-1.6c-0.1-0.3-0.7-1-0.9-1.3c-0.6-0.9-1-1.7-1.6-2.6c-0.2-0.3-0.7-1.1-1-1.1
	c-0.5,0-1,1.3-1.5,0.2c0.4-0.8,1.1-1.4,1.4-2.3c0.1,0.1,0.1,0.3,0.1,0.4c0.3,0.1,0.3-0.1,0.5-0.2c0,0.6,0.3,1.1,0.7,1.4
	c0.2,0.3,0.4,0.5,0.7,0.7C81.2,69.1,81.3,69.3,81.5,69.3z M88.6,77.3c0.1,0.6,0.6,1.3,0.7,1.9c0.3-0.5,0.9-0.9,1-1.6
	c0-0.8-0.6-1.6-1.1-2.1C88.9,76.1,88.5,76.5,88.6,77.3z"/>
<path fill-rule="evenodd" fill="#33342E" d="M87.9,82.4c0.1-0.1,0.4,0,0.5-0.1C88.4,82.4,88.1,82.3,87.9,82.4z"/>
<path fill-rule="evenodd" fill="#33342E" d="M85,77c0.1-0.3,0.7-0.1,1-0.2C85.8,77,85.1,76.7,85,77z"/>
<path fill-rule="evenodd" fill="#33342E" d="M89.2,75.5c0.5,0.5,1.1,1.2,1.1,2.1c0,0.7-0.6,1.1-1,1.6c-0.1-0.6-0.6-1.2-0.7-1.9
	C88.5,76.5,88.9,76.1,89.2,75.5z"/>
<path fill-rule="evenodd" fill="#33342E" d="M83.3,79.9c0,0.2,0.1,0.5-0.1,0.6C83.2,80.3,83.1,80,83.3,79.9z"/>
<path fill-rule="evenodd" fill="#33342E" d="M88.3,79.9c0,0.2,0.1,0.6-0.1,0.7C88.3,80.4,88.2,80,88.3,79.9z"/>
<path fill-rule="evenodd" fill="#33342E" d="M83.7,80.2c0.1,0.2,0.1,0.7,0,0.8C83.7,80.8,83.7,80.5,83.7,80.2z"/>
<path fill-rule="evenodd" fill="#33342E" d="M83.2,80.6c0,0.2,0.1,0.6,0,0.7C83.2,81.1,83.1,80.7,83.2,80.6z"/>
<path fill-rule="evenodd" fill="#33342E" d="M88.9,80.7c0.1,0.1,0,0.4,0.1,0.5C88.8,81.2,88.9,80.9,88.9,80.7z"/>
<path fill-rule="evenodd" fill="#33342E" d="M89.3,80.8c0.1,0.1,0,0.4,0.1,0.5C89.2,81.3,89.3,81,89.3,80.8z"/>
<path fill-rule="evenodd" fill="#33342E" d="M84.8,81.1c0.1,0.1,0.1,0.4,0,0.5C84.8,81.5,84.8,81.3,84.8,81.1z"/>
<path fill-rule="evenodd" fill="#33342E" d="M84.2,80.6c0.1,0.3,0.1,1,0,1.3C84.2,81.5,84.2,81,84.2,80.6z"/>
<g>
	<rect x="67.3" y="60.4" fill-rule="evenodd" fill="#FFFFFF" width="28.5" height="23.2"/>
	<g id="a6fCol_1_">
		<g>
			<g>
				<path fill-rule="evenodd" fill="#D67C28" d="M86.6,77.1c0.1,0.1,0.1,0.5,0,0.6C86.6,77.5,86.6,77.3,86.6,77.1z"/>
				<path fill-rule="evenodd" fill="#F99E44" d="M69,62.1c8.4,0,16.8,0,25.2,0c0,6.6,0,13.2,0,19.8c-8.4,0-16.8,0-25.2,0
					c0-6.5,0-13.1,0-19.6C68.9,62.1,68.9,62.1,69,62.1z M87.3,77.8c0.1-0.5,0.1-1.1-0.1-1.5c0.2,0.1,0.7-0.1,0.8-0.1
					c0,0.1-0.1,0.2-0.1,0.4c1.3-1,0.3-3.7-0.5-4.5c0-0.2,0.1-0.2,0.1-0.4c-0.8-1.4-3.2-1.6-4.4-2.5c-1.4-1-2.4-1.8-3.5-2.9
					c-0.2-0.3-0.4-0.5-0.7-0.7c-0.2-0.1-0.3-0.3-0.5-0.4c-0.4-0.3-0.8-0.8-0.7-1.4c-0.2,0-0.2,0.2-0.5,0.2c0-0.1,0-0.3-0.1-0.4
					c-0.3,0.9-0.9,1.5-1.4,2.3c0.5,1.1,1-0.1,1.5-0.2c0.3,0,0.8,0.7,1,1.1c0.6,1,1,1.7,1.6,2.6c0.2,0.3,0.8,0.9,0.9,1.3
					c0.1,0.5-0.1,1.1-0.1,1.6c0.2,1,0.7,1.9,0.8,2.8c0,0.6-0.3,1.2-0.1,1.8c-0.1,0.1,0,0.4-0.1,0.6c0,0.1,0,0.1,0,0.2
					c-0.1,0.1,0,0.5,0,0.7c0.1,0.5-0.2,0.6-0.3,1c0.1,0.1,0.4,0,0.6,0c0.1-0.4,0.3-0.8,0.3-1.3c0.1-0.2,0.1-0.7,0-0.8
					c0-0.5,0.1-0.9,0.3-1.2c0.1,0.5,0.1,1.1,0.3,1.6c0,0.4,0,0.9,0,1.3c0,0.3-0.3,0.3-0.4,0.6c0.2,0.1,0.4,0.1,0.7,0.1
					c0.1-0.3,0.2-0.6,0.3-1c0.1-0.1,0.1-0.5,0-0.5c-0.1-1,0.2-2.4-0.2-3.3c0.1-0.3,0.3-0.6,0.4-0.9c0.1-0.2,0.8,0,1-0.2
					c0.4,0,0.7-0.1,1-0.3c0.8,0.7,0.9,2.2,1.3,3.3c-0.1,0.1,0,0.5-0.1,0.7c0,0.7-0.2,1.2-0.4,1.8c0,0,0.1,0,0.1,0
					c0.2,0,0.5,0.1,0.5-0.1c0,0,0-0.1,0-0.1c0.1-0.4,0.1-1,0.1-1.5c0.1-0.1,0.1-0.5,0-0.6c0-0.3-0.1-0.8,0.1-1.1
					c0.1,0.5,0.2,1,0.3,1.7c0,0.2-0.1,0.4,0.1,0.5c0.2,0.6-0.1,0.9-0.1,1.4c0.7,0.2,0.6-0.7,0.5-1.3C87.3,78.2,87.4,77.9,87.3,77.8z
					"/>
				<path fill-rule="evenodd" fill="#F4983C" d="M78.8,65.6c0.3,0.2,0.5,0.5,0.7,0.7c-0.2-0.1-0.3-0.2-0.5-0.4
					C79,65.8,78.9,65.8,78.8,65.6z"/>
				<path fill-rule="evenodd" fill="#C86F28" d="M78.4,65.2c0.2,0.1,0.3,0.3,0.5,0.4c0.1,0.1,0.2,0.2,0.3,0.3
					C78.8,65.7,78.6,65.5,78.4,65.2z"/>
				<path fill-rule="evenodd" fill="#C06728" d="M79.5,66.3c1.1,1,2.1,1.9,3.5,2.9c1.2,0.9,3.7,1.1,4.4,2.5c0,0.2-0.1,0.2-0.1,0.4
					c0.8,0.8,1.8,3.5,0.5,4.5c0-0.2,0.1-0.3,0.1-0.4c-0.1,0-0.5,0.2-0.8,0.1c0.1,0.4,0.1,1.1,0.1,1.5c0,0.2-0.1,0.4,0.1,0.5
					c0.1,0.5,0.2,1.5-0.5,1.3c0-0.4,0.3-0.7,0.1-1.4c0-0.2,0.1-0.4-0.1-0.5c0-0.6-0.2-1.1-0.3-1.7c-0.1,0.3,0,0.7-0.1,1.1
					c0,0.2,0,0.4,0,0.6c0,0.5,0,1-0.1,1.5c0,0,0,0,0,0.1c-0.1,0.1-0.4,0-0.5,0.1c0,0-0.1,0-0.1,0c0.2-0.5,0.4-1.1,0.4-1.8
					c0.1-0.1,0-0.5,0.1-0.7c-0.4-1.1-0.5-2.6-1.3-3.3c-0.3,0.1-0.6,0.2-1,0.3c-0.3,0.1-0.9-0.2-1,0.2c-0.1,0.3-0.2,0.6-0.4,0.9
					c0.4,0.9,0.1,2.3,0.2,3.3c0,0.2,0,0.3,0,0.5c-0.1,0.3-0.1,0.7-0.3,1c-0.3,0-0.5,0-0.7-0.1c0.1-0.3,0.4-0.3,0.4-0.6
					c0.1-0.3,0.1-1,0-1.3c-0.1-0.5-0.1-1.1-0.3-1.6c-0.2,0.3-0.3,0.7-0.3,1.2c0,0.3,0,0.5,0,0.8c0,0.5-0.1,0.9-0.3,1.3
					c-0.2,0-0.4,0-0.6,0c0-0.4,0.3-0.5,0.3-1c0.1-0.1,0-0.5,0-0.7c0-0.1,0-0.1,0-0.2c0.1-0.1,0-0.4,0.1-0.6
					c-0.2-0.6,0.1-1.2,0.1-1.8c0-0.9-0.6-1.8-0.8-2.8c-0.1-0.5,0.1-1.1,0.1-1.6c-0.1-0.3-0.7-1-0.9-1.3c-0.6-0.9-1-1.7-1.6-2.6
					c-0.2-0.3-0.7-1.1-1-1.1c-0.5,0-1,1.3-1.5,0.2c0.4-0.8,1.1-1.4,1.4-2.3c0.1,0.1,0.1,0.3,0.1,0.4c0.3,0.1,0.3-0.1,0.5-0.2
					c0,0.6,0.3,1.1,0.7,1.4c0.2,0.3,0.4,0.5,0.7,0.7C79.2,66.1,79.3,66.3,79.5,66.3z M86.6,74.3c0.1,0.6,0.6,1.3,0.7,1.9
					c0.3-0.5,0.9-0.9,1-1.6c0-0.8-0.6-1.6-1.1-2.1C86.9,73.1,86.5,73.5,86.6,74.3z"/>
				<path fill-rule="evenodd" fill="#C86F28" d="M85.9,79.4c0.1-0.1,0.4,0,0.5-0.1C86.4,79.4,86.1,79.3,85.9,79.4z"/>
				<path fill-rule="evenodd" fill="#C86F28" d="M83,74c0.1-0.3,0.7-0.1,1-0.2C83.8,74,83.1,73.7,83,74z"/>
				<path fill-rule="evenodd" fill="#F99E44" d="M87.2,72.5c0.5,0.5,1.1,1.2,1.1,2.1c0,0.7-0.6,1.1-1,1.6c-0.1-0.6-0.6-1.2-0.7-1.9
					C86.5,73.5,86.9,73.1,87.2,72.5z"/>
				<path fill-rule="evenodd" fill="#D67C28" d="M81.3,76.9c0,0.2,0.1,0.5-0.1,0.6C81.2,77.3,81.1,77,81.3,76.9z"/>
				<path fill-rule="evenodd" fill="#D67C28" d="M86.3,76.9c0,0.2,0.1,0.6-0.1,0.7C86.3,77.4,86.2,77,86.3,76.9z"/>
				<path fill-rule="evenodd" fill="#D67C28" d="M81.7,77.2c0.1,0.2,0.1,0.7,0,0.8C81.7,77.8,81.7,77.5,81.7,77.2z"/>
				<path fill-rule="evenodd" fill="#C86F28" d="M81.2,77.6c0,0.2,0.1,0.6,0,0.7C81.2,78.1,81.1,77.7,81.2,77.6z"/>
				<path fill-rule="evenodd" fill="#D67C28" d="M86.9,77.7c0.1,0.1,0,0.4,0.1,0.5C86.8,78.2,86.9,77.9,86.9,77.7z"/>
				<path fill-rule="evenodd" fill="#C86F28" d="M87.3,77.8c0.1,0.1,0,0.4,0.1,0.5C87.2,78.3,87.3,78,87.3,77.8z"/>
				<path fill-rule="evenodd" fill="#C86F28" d="M82.8,78.1c0.1,0.1,0.1,0.4,0,0.5C82.8,78.5,82.8,78.3,82.8,78.1z"/>
				<path fill-rule="evenodd" fill="#D67C28" d="M82.2,77.6c0.1,0.3,0.1,1,0,1.3C82.2,78.5,82.2,78,82.2,77.6z"/>
			</g>
		</g>
	</g>
</g>
<g>
	<polyline fill="none" stroke="#B7B7B8" stroke-width="2" stroke-miterlimit="10" points="48.7,45 48.7,47 46.7,47 	"/>
	
		<line fill="none" stroke="#B7B7B8" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="3.8122,3.8122" x1="42.9" y1="47" x2="14.3" y2="47"/>
	<polyline fill="none" stroke="#B7B7B8" stroke-width="2" stroke-miterlimit="10" points="12.4,47 10.4,47 10.4,45 	"/>
	
		<line fill="none" stroke="#B7B7B8" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="3.936,3.936" x1="10.4" y1="41" x2="10.4" y2="19.4"/>
	<polyline fill="none" stroke="#B7B7B8" stroke-width="2" stroke-miterlimit="10" points="10.4,17.4 10.4,15.4 12.4,15.4 	"/>
	
		<line fill="none" stroke="#B7B7B8" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="3.8122,3.8122" x1="16.2" y1="15.4" x2="44.8" y2="15.4"/>
	<polyline fill="none" stroke="#B7B7B8" stroke-width="2" stroke-miterlimit="10" points="46.7,15.4 48.7,15.4 48.7,17.4 	"/>
	
		<line fill="none" stroke="#B7B7B8" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="3.936,3.936" x1="48.7" y1="21.4" x2="48.7" y2="43"/>
</g>
<g>
	<polyline fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" points="95.6,45 95.6,47 93.6,47 	"/>
	
		<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="3.8122,3.8122" x1="89.8" y1="47" x2="61.2" y2="47"/>
	<polyline fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" points="59.3,47 57.3,47 57.3,45 	"/>
	
		<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="3.936,3.936" x1="57.3" y1="41" x2="57.3" y2="19.4"/>
	<polyline fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" points="57.3,17.4 57.3,15.4 59.3,15.4 	"/>
	
		<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="3.8122,3.8122" x1="63.1" y1="15.4" x2="91.7" y2="15.4"/>
	<polyline fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" points="93.6,15.4 95.6,15.4 95.6,17.4 	"/>
	
		<line fill="none" stroke="#FFFFFF" stroke-width="2" stroke-miterlimit="10" stroke-dasharray="3.936,3.936" x1="95.6" y1="21.4" x2="95.6" y2="43"/>
</g>
<path fill="#33342E" d="M91.3,39.8c0,0-7.4-6.5-15.9-12.4c0,0,0.6,7.8,0.9,19.6l5.6-2.7c4.6,10.6,4.7,16.2,4.8,30h1
	c0,0,5.5-13.9-1.5-32L91.3,39.8z"/>
<path fill="#67C8F0" d="M88.9,36.3c0,0-7.4-6.5-15.9-12.4c0,0,0.6,7.8,0.9,19.6l5.6-2.7c4.6,10.6,7,19.7,7.1,33.4h1
	c0,0,3.2-17.4-3.8-35.5L88.9,36.3z"/>
<rect x="68.9" y="83.6" fill-rule="evenodd" fill="#8C8C8C" width="2.6" height="3"/>
</svg>
