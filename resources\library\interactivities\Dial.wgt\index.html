<!doctype html>
<html xmlns="http://www.w3.org/1999/xhtml">

    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

        <link rel="stylesheet" href="css/ubw-main.css">
            <link rel="stylesheet" href="css/main.css">

                <script src="js/templates.js" type="text/javascript" charset="utf-8"></script>

                <script src="js/lib/modernizr.js" type="text/javascript"></script>
                <script src="js/lib/jquery-1.7.1.min.js" type="text/javascript"></script>
                <script src="js/lib/jquery-css-transform.js" type="text/javascript"></script>
                <script src="js/lib/jquery.i18n.properties-min-1.0.9.js" type="text/javascript" charset="utf-8"></script>
                <script src="js/lib/mustache.js" type="text/javascript"></script>
                <script src="js/lib/ubw-main.js" type="text/javascript"></script>
                <script src="js/main.js" type="text/javascript" charset="utf-8"></script>
                <script type="text/javascript">
                    $(document).ready(function(){
                        $(".circle.input>div").keydown(function(){
                            if((event.keyCode < 48 || event.keyCode > 57) && event.keyCode != 189 && event.keyCode != 46 && event.keyCode != 8 && event.keyCode != 37 && event.keyCode != 39)
                                return false;
                            if(event.keyCode == 46 || event.keyCode == 8 || event.keyCode == 37 || event.keyCode == 39)
                                return true;
                            else{
                                if(this.innerHTML != "<br>")
                                    return (this.innerHTML.charAt(0) == '-')?(this.innerHTML.length > 2)?false:true:(this.innerHTML.length > 1)?(event.keyCode == 189)?true:false:true;
                                else
                                    return true;
                            }
                        })
                    })
                </script>
                </head>

                <body class="hasHelp">
                    <div id="ubwidget" data-themes="pad">
                        <div class="wrapper">
                            <div id="toolbar">
                            </div>
                            <div id="help">
                                <iframe src="help.html" width="100%" height="100%" frameborder="0"></iframe>
                            </div>
                            <div id="content">
                                <div id="parameters">
                                </div>
                                <div id="scene">
                                    <div id="disc">
                                        <div class="circle result hide">
                                            <div>
                                                <div id="result">
                                                    <div>
                                                        <div class="front">?</div>
                                                        <div class="back">12</div>	
                                                    </div>
                                                </div>									
                                            </div>
                                        </div>
                                        <div class="circle numbers">
                                            <div><div>0</div></div>
                                            <div><div>1</div></div>
                                            <div><div>2</div></div>
                                            <div><div>3</div></div>
                                            <div><div>4</div></div>
                                            <div><div>5</div></div>
                                            <div><div>6</div></div>
                                            <div><div>7</div></div>
                                            <div><div>8</div></div>
                                            <div><div>9</div></div>
                                            <div><div>10</div></div>
                                            <div><div>11</div></div>
                                        </div>
                                        <div class="circle operators">
                                            <div><div>+</div></div>
                                            <div><div>*</div></div>
                                            <div><div>-</div></div>
                                            <div><div>/</div></div>
                                        </div>
                                        <div class="circle input">
                                            <div></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </body>

                </html>
