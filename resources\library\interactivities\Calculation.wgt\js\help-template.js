<h3> Calculation </h3>
<h4> Arithmetic, simple operations </h4>
 
<p>Interactivity trains add, subtract and multiply numbers. The goal is to enter the correct results. To check the answer click on the "v" button. If the result is incorrect, the calculation turns in red. If the result is correct, the calculation turns in green. </p>
<p>Once the series is performed, the interactivity reviews all calculations</p>
 
<p>"Reload" button generates new calculations. </p>
 
<p>Enter the "Edit" mode to choose the : </p>
<ul> <li>the theme of the App : pad, slate, or none (by default : pad), </li>
<li> operation (addition, subtraction, multiplication),</li>
<li> magnitude of proposed numbers,</li>
<li> number of elements (2-4),</li>
<li> number of operations (2-10). </li> </ul>

<p> Results are automatically calculated by the interactivity. </p>
<p>"Display" button comes back to the activity.</p>
