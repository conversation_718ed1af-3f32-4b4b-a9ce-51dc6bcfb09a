html, body{
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    border-radius: 35px;
    overflow: hidden;
    font-family: sans-serif;
}

body{
    background-image: url(../img/bg.png);
}

.toggle_mode{
    width: 100%;
    height: 25px;
    border-bottom: 1px solid #999;
    background-color: #ccc;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 2;
}

#display_img, #edit_img{
    width: 15px;
    height: 15px;
    margin: 5px 10px;
}

#display_text, #edit_text{
    height: 19px;
    padding: 3px 0 0 0;
}

.selected{

}

#display:hover, #edit:hover{
    background-color: #999;
}

.img_block{
/*    width: 120px;
    height: 120px;
    padding: 5px;*/
    display: inline-block;
    position: absolute;
    top: 100px;
    left: 350px;
    -webkit-user-select: none;
}

.img_block img{
    vertical-align: middle;
}

.img_gray{
    border: 1px solid #ccc;
    -moz-box-shadow: #ccc -1px 0 4px;
    -webkit-box-shadow: #ccc -1px 0 4px;
    box-shadow: #ccc -1px 0 4px;
}

.close_img {
    width: 30px;
    height: 30px;
    background-repeat: no-repeat;
    position: absolute;
    background-image: url(../img/close_img.png);    
    top: -15px;   
    right: -15px;
    cursor: pointer;
}

.size_up{
    width: 20px;
    height: 20px;
    background-repeat: no-repeat;
    position: absolute;
    background-image: url(../img/size_up.png);    
    top: -15px;   
    right: 20px;
    cursor: pointer;
}

.size_down{
    width: 20px;
    height: 20px;
    background-repeat: no-repeat;
    position: absolute;
    background-image: url(../img/size_down.png);
    top: -15px;   
    right: 45px;
    cursor: pointer;
}

.dropHere{
    background-color: #ccc;
}

.dropBack{
    background-color: #F4F2F2;
}

.audio_block{
    width: 120px;
    height: 30px;
    padding: 10px 5px;    
    position: absolute;
    top: 200px;
    left: 50px;
}

.audio_desc{
    min-height: 30px;
    margin: 5px 0;
    float: left;
    margin-left: 20px;
}

.play, .stop{
    height: 30px;
    width: 70px;    
    float: left;
    cursor: pointer;
}

.play{
    background-image: url(../img/play.png);
}

.stop{
    background-image: url(../img/stop.png);
}

.replay{
    height: 30px;
    width: 32px;
    margin-left: 10px;
    float: left;
    background-image: url(../img/replay.png);
    background-repeat: no-repeat;
    cursor: pointer;
}

.gray{
    background-color: #ccc;
}

#slider ul, #slider li{
    margin:0;
    padding:0;
    list-style:none;
}

#slider{ 
    height: 100%;
    width: 100%;
    overflow:hidden; 
}

span#prevBtn a{
    height: 100%;
    width: 47px;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0.7;
    background-image: url(../img/turn_left.png);
    background-repeat: no-repeat;
    background-position: 50% 50%;
}

span#nextBtn a{
    height: 100%;
    width: 47px;
    position: absolute;
    opacity: 0.7;
    right: 0;
    top: 0;
    background-image: url(../img/turn_right.png);
    background-repeat: no-repeat;
    background-position: 50% 50%;
}

span#nextBtn a:hover, span#prevBtn a:hover{
    opacity: 1;
}

li>div{
    width: 100%;
    height: 100%;

    position: relative;
}

.text_block{
    margin: 0;
    padding: 5px;
    width: 200px;
    position: absolute;
    top: 50px;
    left: 100px;
    background-color: white;
    border-radius: 10px;
}

.real_text{
    width: 100%;
    height: 100%;
    word-wrap: break-word;
}

.block_border{
    border: 1px solid #999;
    border-radius: 5px;
}

.move_block{
    width: 30px;
    height: 30px;
    background-image: url(../img/moving.png);
    position: absolute;
    top: -15px;
    left: -15px;
    cursor: pointer;
}

.resize_block{
    width: 30px;
    height: 30px;
    background-image: url(../img/resize.png);
    position: absolute;
    bottom: -13px;
    right: -13px;
    cursor: pointer;
}

.add_left{
    width: 41px;
    height: 30px;
    opacity: 0.5;
    background-image: url(../img/add_left.png);
    position: absolute;
    bottom: 40px;
    left: 40px;
    cursor: pointer;
}

.add_right{
    width: 41px;
    height: 30px;
    opacity: 0.5;
    background-image: url(../img/add_right.png);
    position: absolute;
    bottom: 40px;
    right: 80px;
    cursor: pointer;
}

.close_slide{
    width: 30px;
    height: 30px;
    opacity: 0.5;
    background-image: url(../img/close_slide.png);
    position: absolute;
    top: 40px;
    right: 80px;
    cursor: pointer;
}

.add_text{
    width: 30px;
    height: 30px;
    opacity: 0.5;
    background-image: url(../img/add_text.png);
    position: absolute;
    top: 40px;
    left: 40px;
    cursor: pointer;
}

.add_left:hover, .add_right:hover, .close_slide:hover, .add_text:hover{
    opacity: 0.8;
}

/*new design*/

.body_table{
    width: 100%;
    height: 100%;
    border-spacing: 0;
}

/*top*/

.b_top_left{
    width: 54px;
    background-image: url(../img/top_left.png);
    background-repeat: no-repeat;
}

.b_top_right{
    width: 54px;
    background-image: url(../img/top_right.png);
    background-repeat: no-repeat;
}

.b_top_center{
    height: 54px;
    background-image: url(../img/top.png);
    background-repeat: repeat-x;
}

/*bottom*/

.b_bottom_left{
    width: 54px;
    background-image: url(../img/bottom_left.png);
    background-repeat: no-repeat;
}

.b_bottom_right{
    width: 54px;
    background-image: url(../img/bottom_right.png);
    background-repeat: no-repeat;
}

.b_bottom_center{
    background-image: url(../img/bottom.png);
    background-repeat: repeat-x;
}

/*center*/

.b_center_left{
    width: 54px;
    background-image: url(../img/left.png);
    background-repeat: repeat-y;
}

.b_center_right{
    width: 54px;
    background-image: url(../img/right.png);
    background-repeat: repeat-y;
}

#data{
    width: 100%;
    height: 100%;    
    min-height: 250px;
    overflow: auto;
}

#wgt_name{
    height: 44px;
    margin: 10px 10px 0 10px;
    padding: 0;
    float: left;
    font-family: sans-serif;
    font-size: 24px;
    color: #7F613F;
    text-shadow: #FFDCA9 0 1px 0;
}

#wgt_edit, #wgt_display, #wgt_help{
    cursor: pointer;
    margin: 10px 10px 0 0;
    float: right;
    font-family: sans-serif;
    font-size: 24px;    
    line-height: 32px;
}

#wgt_display{
    padding-left: 35px;
    background: url(../img/toolbar-edit.png) left -32px no-repeat;
    color: white;
    display: none;
}

#wgt_edit{    
    padding-left: 35px;
    background: url(../img/slate-toolbar-edit.png) left top no-repeat;
    color: #7F613F;
    text-shadow: #FFDCA9 0 1px 0;
}

.btl_pad{
    background-image: url(../img/pad-top-left.png) !important;
}

.btc_pad{
    background-image: url(../img/pad-top.png) !important;
}

.btr_pad{
    background-image: url(../img/pad-top-right.png) !important;
}

.bcl_pad{
    background-image: url(../img/pad-left.png) !important;
}

.bcr_pad{
    background-image: url(../img/pad-right.png) !important;
}

.bbl_pad{
    background-image: url(../img/pad-bottom-left.png) !important;
}

.bbc_pad{
    background-image: url(../img/pad-bottom.png) !important;
}

.bbr_pad{
    background-image: url(../img/pad-bottom-right.png) !important;
}

.without_radius{
    border-radius: 0 !important;
}

.without_back{
    background: none !important;
}

.pad_color{
    color: #FC9 !important;
    text-shadow: none !important;
}

.pad_reload{
    background: url(../img/toolbar-reload.png) left top no-repeat !important;
}

.pad_edit{
    background: url(../img/toolbar-edit.png) left top no-repeat !important;    
}

.pad_help{
    background: url(../img/toolbar-help.png) left top no-repeat !important;
}

.help_wood{
    background: url(../img/slate-toolbar-help.png) left -32px no-repeat !important;
    color: white !important;
    text-shadow: #7F613F 0 -1px 0 !important;
}

.help_pad{
    background: url(../img/toolbar-help.png) left -32px no-repeat !important;
    color: white !important;
}

#wgt_help{
    padding-left: 35px;
    background: url(../img/slate-toolbar-help.png) left top no-repeat;
    color: #7F613F;
    text-shadow: #FFDCA9 0 1px 0;
}

#help{
    height: 100%;    
    overflow: auto;
    display: none;
    background-color: #ccc;
    padding: 5px;
}

.open{

}

#parameters{    
    display: none;
    padding: 10px 20px;
    background: url("../img/parameters-bg.png");
    border-radius: 4px 4px 0 0;
}

.inline{
    display: inline-block;
    font-family: sans-serif;
    font-size: 14px;
    color: #666;
}

#parameters label {
    font-style: italic;
}

#style_select{   
    margin-left: 10px;
}

.display_wood{
    background: url(../img/slate-toolbar-edit.png) left -32px no-repeat !important;
    text-shadow: #7F613F 0 -1px 0;
}

.radius_ft{
    border-radius: 45px !important;
}