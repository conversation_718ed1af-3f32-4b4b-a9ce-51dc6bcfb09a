<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Slides Viewer Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        
        .demo-header {
            background: #4285f4;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .demo-content {
            padding: 20px;
        }
        
        .url-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            margin-bottom: 16px;
            box-sizing: border-box;
        }
        
        .url-input:focus {
            outline: none;
            border-color: #4285f4;
            box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
        }
        
        .controls {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 16px;
            flex-wrap: wrap;
        }
        
        .btn {
            background: #4285f4;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 8px 16px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s ease;
        }
        
        .btn:hover {
            background: #3367d6;
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .slide-info {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }
        
        .slide-input {
            width: 60px;
            padding: 4px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
        }
        
        .iframe-container {
            width: 100%;
            height: 600px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }
        
        .slides-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .instructions {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #4285f4;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #333;
        }
        
        .instructions ol {
            margin-bottom: 0;
            color: #666;
            line-height: 1.6;
        }
        
        .example-urls {
            background: #e8f0fe;
            padding: 12px;
            border-radius: 6px;
            margin-top: 12px;
        }
        
        .example-urls strong {
            color: #1a73e8;
        }
        
        .example-urls code {
            display: block;
            margin: 4px 0;
            padding: 4px 8px;
            background: white;
            border-radius: 4px;
            font-size: 12px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🎯 Google Slides Viewer Demo</h1>
            <p>Test the browser-based Google Slides integration for Excalidraw</p>
        </div>
        
        <div class="demo-content">
            <div class="instructions">
                <h3>📋 How to use:</h3>
                <ol>
                    <li>Open your Google Slides presentation</li>
                    <li>Click "Share" and make sure it's viewable by anyone with the link</li>
                    <li>Copy the URL from your browser</li>
                    <li>Paste it in the input field below</li>
                    <li>Navigate through slides using the controls!</li>
                </ol>
                
                <div class="example-urls">
                    <strong>Example URLs that work:</strong>
                    <code>https://docs.google.com/presentation/d/1ABC123.../edit</code>
                    <code>https://docs.google.com/presentation/d/1ABC123.../view</code>
                </div>
            </div>
            
            <input 
                type="url" 
                id="urlInput"
                class="url-input" 
                placeholder="Paste Google Slides URL here..."
            />
            
            <div class="controls">
                <button class="btn" onclick="loadPresentation()">📊 Load Presentation</button>
                <button class="btn" onclick="navigateSlide(-1)" id="prevBtn" disabled>← Previous</button>
                
                <div class="slide-info">
                    <span>Slide</span>
                    <input type="number" id="slideInput" class="slide-input" value="1" min="1" onchange="goToSlide()">
                    <span>of <span id="totalSlides">1</span></span>
                </div>
                
                <button class="btn" onclick="navigateSlide(1)" id="nextBtn" disabled>Next →</button>
                
                <button class="btn" onclick="togglePresentMode()" id="presentBtn" disabled>🖥️ Present Mode</button>
            </div>
            
            <div class="iframe-container">
                <iframe id="slidesFrame" class="slides-iframe" src="about:blank"></iframe>
            </div>
        </div>
    </div>

    <script>
        let currentSlide = 1;
        let totalSlides = 1;
        let presentationId = '';
        let isPresentMode = false;
        
        function extractPresentationId(url) {
            const patterns = [
                /\/presentation\/d\/([a-zA-Z0-9-_]+)/,
                /\/presentation\/d\/([a-zA-Z0-9-_]+)\/edit/,
                /\/presentation\/d\/([a-zA-Z0-9-_]+)\/view/
            ];
            
            for (const pattern of patterns) {
                const match = url.match(pattern);
                if (match) return match[1];
            }
            return null;
        }
        
        function generateEmbedUrl(id, slide = 1) {
            if (isPresentMode) {
                return `https://docs.google.com/presentation/d/${id}/present?start=false&loop=false&delayms=3000&slide=${slide}`;
            } else {
                return `https://docs.google.com/presentation/d/${id}/embed?start=false&loop=false&delayms=3000&slide=${slide}`;
            }
        }
        
        function loadPresentation() {
            const url = document.getElementById('urlInput').value;
            const id = extractPresentationId(url);
            
            if (!id) {
                alert('Invalid Google Slides URL. Please make sure you\'re using a valid Google Slides link.');
                return;
            }
            
            presentationId = id;
            currentSlide = 1;
            totalSlides = 20; // Default assumption, could be improved with API
            
            updateSlideDisplay();
            updateControls();
            
            // Load the first slide
            const iframe = document.getElementById('slidesFrame');
            iframe.src = generateEmbedUrl(id, currentSlide);
        }
        
        function navigateSlide(direction) {
            if (!presentationId) return;
            
            const newSlide = currentSlide + direction;
            if (newSlide < 1 || newSlide > totalSlides) return;
            
            currentSlide = newSlide;
            updateSlideDisplay();
            updateControls();
            
            const iframe = document.getElementById('slidesFrame');
            iframe.src = generateEmbedUrl(presentationId, currentSlide);
        }
        
        function goToSlide() {
            if (!presentationId) return;
            
            const slideInput = document.getElementById('slideInput');
            const slide = parseInt(slideInput.value) || 1;
            
            if (slide < 1 || slide > totalSlides) {
                slideInput.value = currentSlide;
                return;
            }
            
            currentSlide = slide;
            updateControls();
            
            const iframe = document.getElementById('slidesFrame');
            iframe.src = generateEmbedUrl(presentationId, currentSlide);
        }
        
        function togglePresentMode() {
            if (!presentationId) return;
            
            isPresentMode = !isPresentMode;
            const presentBtn = document.getElementById('presentBtn');
            presentBtn.textContent = isPresentMode ? '📋 Edit Mode' : '🖥️ Present Mode';
            
            const iframe = document.getElementById('slidesFrame');
            iframe.src = generateEmbedUrl(presentationId, currentSlide);
        }
        
        function updateSlideDisplay() {
            document.getElementById('slideInput').value = currentSlide;
            document.getElementById('totalSlides').textContent = totalSlides;
        }
        
        function updateControls() {
            document.getElementById('prevBtn').disabled = currentSlide <= 1 || !presentationId;
            document.getElementById('nextBtn').disabled = currentSlide >= totalSlides || !presentationId;
            document.getElementById('presentBtn').disabled = !presentationId;
        }
        
        // Initialize
        updateControls();
    </script>
</body>
</html>
