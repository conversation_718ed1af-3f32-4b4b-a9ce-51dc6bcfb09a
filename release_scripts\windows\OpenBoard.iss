; Script generated by the Inno Setup Script Wizard.
; SEE THE DOCUMENTATION FOR DETAILS ON CREATING INNO SETUP SCRIPT FILES!

#define ApplicationVersion GetFileVersion("..\..\build\win32\release\product\OpenBoard.exe")


[Setup]
; NOTE: The value of AppId uniquely identifies this application.
; Do not use the same AppId value in installers for other applications.
; (To generate a new GUID, click Tools | Generate GUID inside the IDE.)
AppId={{8CCA6AC7-BBF9-4DD2-8E70-A907E0FCA38F}}
AppName=OpenBoard
AppVersion={#ApplicationVersion}
AppVerName=OpenBoard {#ApplicationVersion}
UninstallDisplayName=OpenBoard {#ApplicationVersion}
AppPublisher=Open Education Foundation
ArchitecturesInstallIn64BitMode=x64

AppPublisherURL=http://www.oe-f.org
AppSupportURL=http://www.openboard.org
AppUpdatesURL=http://get.openboard.org

DefaultDirName={pf}\OpenBoard
DefaultGroupName=OpenBoard

#define ProjectRoot GetEnv('PROJECT_ROOT')

OutputDir={#ProjectRoot}\install\win32\
OutputBaseFilename=OpenBoard
SetupIconFile={#ProjectRoot}\resources\win\OpenBoard.ico
Compression=lzma
SolidCompression=yes

[Languages]
Name: "en"; MessagesFile: "compiler:Default.isl"
Name: "fr"; MessagesFile: "compiler:Languages\French.isl"
Name: "gr"; MessagesFile: "compiler:Languages\German.isl"
Name: "it"; MessagesFile: "compiler:Languages\Italian.isl"
Name: "sp"; MessagesFile: "compiler:Languages\Spanish.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[InstallDelete]

Type: files ; Name: "{app}\OpenBoard.pdb"
Type: filesandordirs ; Name: "{app}\library"
Type: filesandordirs ; Name: "{app}\Microsoft.VC90.CRT"
Type: filesandordirs ; Name: "{app}\imageformats"
Type: filesandordirs ; Name: "{app}\iconengines"
Type: filesandordirs ; Name: "{app}\multimedia"
Type: filesandordirs ; Name: "{app}\platforms"
Type: filesandordirs ; Name: "{app}\resources"
Type: filesandordirs ; Name: "{app}\tls"
Type: filesandordirs ; Name: "{app}\translations"
Type: filesandordirs ; Name: "{app}\i18n"
Type: files ; Name: "{app}\*.dll"


#define QtLibs GetEnv('QT_BIN')
#define QtDir GetEnv('QT_DIR')

[Files]
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\microsoft\vcredist_2013.x64.exe"; DestDir:"{tmp}"
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\microsoft\vcredist_2015_2022.x64.exe"; DestDir:"{tmp}"
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\microsoft\LAVFilters-0.77.2-Installer.exe"; DestDir:"{tmp}"
Source: "{#ProjectRoot}\build\win32\release\product\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

;OpenSSL
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\openssl\openssl-3.0.15-win64\bin\libssl-3-x64.dll"; DestDir:"{app}"; Flags: ignoreversion
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\openssl\openssl-3.0.15-win64\bin\libcrypto-3-x64.dll"; DestDir:"{app}"; Flags: ignoreversion
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\openssl\win32\libeay32.dll"; DestDir:"{app}"; Flags: ignoreversion
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\openssl\win32\ssleay32.dll"; DestDir:"{app}"; Flags: ignoreversion

;Qt base dll
;Source: "OpenBoard.exe"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6Concurrent.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6Core.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6Gui.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6Multimedia.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6MultimediaWidgets.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6Network.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6Opengl.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6PrintSupport.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6Qml.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6Sql.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6Svg.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6SvgWidgets.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6Widgets.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6Xml.dll"; DestDir: "{app}" 
Source: "{#QtLibs}\Qt6Quick.dll"; DestDir: "{app}"  
Source: "{#QtLibs}\Qt6Positioning.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6QmlModels.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6WebChannel.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6WebEngineCore.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6WebEngineWidgets.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6QuickWidgets.dll"; DestDir: "{app}"
Source: "{#QtLibs}\Qt6Core5Compat.dll"; DestDir: "{app}" 
Source: "{#QtLibs}\double-conversion.dll"; DestDir: "{app}" 
Source: "{#QtLibs}\icudt74.dll"; DestDir: "{app}" 
Source: "{#QtLibs}\icuin74.dll"; DestDir: "{app}" 
Source: "{#QtLibs}\pcre2-16.dll"; DestDir: "{app}" 
Source: "{#QtLibs}\harfbuzz.dll"; DestDir: "{app}" 
Source: "{#QtLibs}\icuuc74.dll"; DestDir: "{app}" 
Source: "{#QtLibs}\libcrypto-3-x64.dll"; DestDir: "{app}" 
Source: "{#QtLibs}\zstd.dll"; DestDir: "{app}" 
;Source: "/etc/freezedWidgetWrapper.html"; DestDir: "{app}"  	
;Source: "*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs


; Poppler dlls
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\poppler\bin\brotlicommon.dll"; DestDir: "{app}"
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\poppler\bin\brotlidec.dll"; DestDir: "{app}"
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\poppler\bin\bz2.dll"; DestDir: "{app}"
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\poppler\bin\freetype.dll"; DestDir: "{app}"
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\poppler\bin\jpeg62.dll"; DestDir: "{app}"
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\poppler\bin\liblzma.dll"; DestDir: "{app}"
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\poppler\bin\libpng16.dll"; DestDir: "{app}"
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\poppler\bin\openjp2.dll"; DestDir: "{app}"
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\poppler\bin\poppler.dll"; DestDir: "{app}"
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\poppler\bin\poppler-cpp.dll"; DestDir: "{app}"
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\poppler\bin\tiff.dll"; DestDir: "{app}"
Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\poppler\bin\zlib1.dll"; DestDir: "{app}"

Source: "{#ProjectRoot}\..\OpenBoard-ThirdParty\zlib\1.2.11\bin\zlib.dll"; DestDir:"{app}"; Flags: ignoreversion

; NOTE: Don't use "Flags: ignoreversion" on any shared system files

;Qt windows plugins
Source: "{#QtDir}\Qt6\plugins\platforms\qminimal.dll"; DestDir: "{app}\platforms"; Flags: ignoreversion
Source: "{#QtDir}\Qt6\plugins\platforms\qoffscreen.dll"; DestDir: "{app}\platforms"; Flags: ignoreversion
Source: "{#QtDir}\Qt6\plugins\platforms\qwindows.dll"; DestDir: "{app}\platforms"; Flags: ignoreversion


;Qt images formats plugins
Source: "{#QtDir}\Qt6\plugins\imageformats\qgif.dll"; DestDir: "{app}\imageformats"; Flags: ignoreversion
Source: "{#QtDir}\Qt6\plugins\imageformats\qico.dll"; DestDir: "{app}\imageformats"; Flags: ignoreversion
Source: "{#QtDir}\Qt6\plugins\imageformats\qjpeg.dll"; DestDir: "{app}\imageformats"; Flags: ignoreversion
Source: "{#QtDir}\Qt6\plugins\imageformats\qsvg.dll"; DestDir: "{app}\imageformats"; Flags: ignoreversion

;qt icon engine plugin
Source: "{#QtDir}\Qt6\plugins\iconengines\qsvgicon.dll"; DestDir: "{app}\iconengines"; Flags: ignoreversion

;qt multimedia plugin
Source: "{#QtDir}\Qt6\plugins\multimedia\windowsmediaplugin.dll"; DestDir: "{app}\multimedia"; Flags: ignoreversion

;qt tls dependencies
Source: "{#QtDir}\Qt6\plugins\tls\qcertonlybackend.dll"; DestDir: "{app}\tls"; Flags: ignoreversion
Source: "{#QtDir}\Qt6\plugins\tls\qopensslbackend.dll"; DestDir: "{app}\tls"; Flags: ignoreversion
Source: "{#QtDir}\Qt6\plugins\tls\qschannelbackend.dll"; DestDir: "{app}\tls"; Flags: ignoreversion

;QtWebEngine dependencies
Source: "{#QtLibs}\QtWebEngineProcess.exe"; DestDir: "{app}"
Source: "{#QtDir}\share\Qt6\resources\*"; DestDir: "{app}\resources"

;Qt translations
Source: "{#QtDir}\translations\Qt6\*"; DestDir: "{app}\i18n"
Source: "{#QtDir}\translations\Qt6\qtwebengine_locales\*"; DestDir: "{app}\translations\Qt6\qtwebengine_locales"

;fonts for xpdf
Source: "{#ProjectRoot}\resources\windows\xpdfrc"; DestDir: "{app}"; Flags: ignoreversion
Source: "{#ProjectRoot}\resources\fonts\*"; DestDir: "{app}\fonts"; Flags: ignoreversion

[Icons]
Name: "{group}\OpenBoard"; Filename: "{app}\OpenBoard.exe"
Name: "{group}\{cm:UninstallProgram,OpenBoard}"; Filename: "{uninstallexe}"
Name: "{commondesktop}\OpenBoard"; Filename: "{app}\OpenBoard.exe"; Tasks: desktopicon
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\OpenBoard"; Filename: "{app}\OpenBoard.exe"; Tasks: quicklaunchicon

[Registry]
Root: HKCR; Subkey: ".ubz"; ValueType: string; ValueName: ""; ValueData: "OpenBoardFile"; Flags: uninsdeletevalue
Root: HKCR; Subkey: "OpenBoardFile"; ValueType: string; ValueName: ""; ValueData: "OpenBoard document"; Flags: uninsdeletekey
Root: HKCR; Subkey: "OpenBoardFile\DefaultIcon"; ValueType: string; ValueName: ""; ValueData: "{app}\OpenBoard.exe,1"
Root: HKCR; Subkey: "OpenBoardFile\shell\open\command"; ValueType: string; ValueName: ""; ValueData: """{app}\OpenBoard.exe"" ""%1"""

Root: HKLM; Subkey: "SOFTWARE\OpenBoard"; ValueType: string; ValueName: "Client application"; ValueData: "{app}\OpenBoard.exe"; Flags: uninsdeletevalue; Check: isProcessorNotX64
Root: HKLM; Subkey: "SOFTWARE\OpenBoard"; ValueType: dword; ValueName: "Transfer mode"; ValueData: "0"; Flags: uninsdeletevalue; Check: isProcessorNotX64
Root: HKLM; Subkey: "SOFTWARE\OpenBoard"; ValueType: dword; ValueName: "EMF: Hide page"; ValueData: "1"; Flags: uninsdeletevalue; Check: isProcessorNotX64
Root: HKLM; Subkey: "SOFTWARE\OpenBoard\Defaults"; ValueType: dword; ValueName: "PDF: Enabled"; ValueData: "1"; Flags: uninsdeletevalue; Check: isProcessorNotX64

Root: HKLM; Subkey: "SOFTWARE\Microsoft\Internet Explorer\Low Rights\DragDrop\{{E63D17F8-D9DA-479D-B9B5-0D101A03703B}"; ValueType: dword; ValueName: "Policy"; ValueData: "3"; Flags: uninsdeletevalue; Check: isProcessorNotX64
Root: HKLM; Subkey: "SOFTWARE\Microsoft\Internet Explorer\Low Rights\DragDrop\{{E63D17F8-D9DA-479D-B9B5-0D101A03703B}"; ValueType: string; ValueName: "AppName"; ValueData: "OpenBoard.exe"; Flags: uninsdeletevalue; Check: isProcessorNotX64
Root: HKLM; Subkey: "SOFTWARE\Microsoft\Internet Explorer\Low Rights\DragDrop\{{E63D17F8-D9DA-479D-B9B5-0D101A03703B}"; ValueType: string; ValueName: "AppPath"; ValueData: "{app}"; Flags: uninsdeletevalue; Check: isProcessorNotX64

Root: HKLM64; Subkey: "SOFTWARE\OpenBoard"; ValueType: string; ValueName: "Client application"; ValueData: "{app}\OpenBoard.exe"; Flags: uninsdeletevalue; Check: isProcessorX64
Root: HKLM64; Subkey: "SOFTWARE\OpenBoard"; ValueType: dword; ValueName: "Transfer mode"; ValueData: "0"; Flags: uninsdeletevalue; Check: isProcessorX64
Root: HKLM64; Subkey: "SOFTWARE\OpenBoard"; ValueType: dword; ValueName: "EMF: Hide page"; ValueData: "1"; Flags: uninsdeletevalue; Check: isProcessorX64
Root: HKLM64; Subkey: "SOFTWARE\OpenBoard\Defaults"; ValueType: dword; ValueName: "PDF: Enabled"; ValueData: "1"; Flags: uninsdeletevalue; Check: isProcessorX64

Root: HKLM64; Subkey: "SOFTWARE\Wow6432Node\Microsoft\Internet Explorer\Low Rights\DragDrop\{{E63D17F8-D9DA-479D-B9B5-0D101A03703B}"; ValueType: dword; ValueName: "Policy"; ValueData: "3"; Flags: uninsdeletevalue; Check: isProcessorX64
Root: HKLM64; Subkey: "SOFTWARE\Wow6432Node\Microsoft\Internet Explorer\Low Rights\DragDrop\{{E63D17F8-D9DA-479D-B9B5-0D101A03703B}"; ValueType: string; ValueName: "AppName"; ValueData: "OpenBoard.exe"; Flags: uninsdeletevalue; Check: isProcessorX64
Root: HKLM64; Subkey: "SOFTWARE\Wow6432Node\Microsoft\Internet Explorer\Low Rights\DragDrop\{{E63D17F8-D9DA-479D-B9B5-0D101A03703B}"; ValueType: string; ValueName: "AppPath"; ValueData: "{app}"; Flags: uninsdeletevalue; Check: isProcessorX64

[Run]
Filename: "{tmp}\vcredist_2013.x64.exe";WorkingDir:"{tmp}"; Parameters: "/install /quiet /norestart"; StatusMsg: Installing CRT 2013...
Filename: "{tmp}\vcredist_2015_2022.x64.exe";WorkingDir:"{tmp}"; Parameters: "/install /quiet /norestart"; StatusMsg: Installing CRT 2015-2022 ...
Filename: "{tmp}\LAVFilters-0.77.2-Installer.exe";WorkingDir:"{tmp}"; Parameters: "/VERYSILENT /SUPPRESSMSGBOXES /NORESTART"; StatusMsg: Installing LAV Filters ...
Filename: "{app}\OpenBoard.exe"; Description: "{cm:LaunchProgram,OpenBoard}"; Flags: nowait postinstall skipifsilent 

[UninstallDelete]
; cleanup and delete whole installation directory
Name: {app}; Type: filesandordirs

[Code]
function isProcessorX64: Boolean;
begin
  Result := (ProcessorArchitecture = paX64);
end;

function isProcessorNotX64: Boolean;
begin
	Result := not isProcessorX64;
end;

