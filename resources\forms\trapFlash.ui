<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>trapFlashDialog</class>
 <widget class="QDialog" name="trapFlashDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>508</width>
    <height>417</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Capture Web Content</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_3" stretch="0,1">
     <item>
      <widget class="QLabel" name="selectFlashLabel">
       <property name="text">
        <string>Select a content to capture</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QComboBox" name="flashCombobox"/>
     </item>
    </layout>
   </item>
   <item>
    <widget class="QScrollArea" name="scrollArea">
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>488</width>
        <height>298</height>
       </rect>
      </property>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <property name="leftMargin">
        <number>0</number>
       </property>
       <property name="topMargin">
        <number>0</number>
       </property>
       <property name="rightMargin">
        <number>0</number>
       </property>
       <property name="bottomMargin">
        <number>0</number>
       </property>
       <item>
        <widget class="QWebEngineView" name="webView" native="true">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <property name="url" stdset="0">
          <url>
           <string>about:blank</string>
          </url>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <item>
        <widget class="QLabel" name="label">
         <property name="text">
          <string>Application name</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QLineEdit" name="widgetNameLineEdit"/>
     </item>
    </layout>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout_4">
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="createWidgetButton">
       <property name="text">
        <string>Create Application</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QWebEngineView</class>
   <extends>QWidget</extends>
   <header>QWebEngineView</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
