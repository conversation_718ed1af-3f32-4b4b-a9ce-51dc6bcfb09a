html, body{
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;    
    border-radius: 35px;
    overflow: hidden;
    font-family: sans-serif;
}

body{
    background-image: url(../img/bg.png);
}

.letter{
    min-width: 30px; 
    max-width: 600px;
    height: 40px;
    overflow: hidden;
    border: solid 1px #ccc;
    margin: 4px;
    padding: 4px 10px;
    float: left;
    font: 28px tahoma;
    text-align: center;
    cursor: pointer;
    background-color: #fff;
}

.right{
    background-color: #cfb;
}

#mp_setup{
    text-align: right;
}

#mp_img{
    text-align: center;
    padding: 40px;
}

#mp_word{
    height: 60px;
    text-align: center;
}

#mp_word .wgt_cont{
    margin-top: 20px;
    font: 32px tahoma;
    letter-spacing: 0.1em;
    width: 98%;
    min-height: 200px;
    resize: vertical;
}

#mp_msg{
    position: absolute;
    left: 120px; top: 160px;
    background-color: #eee;
    border: solid 1px #ddd;
    width: 160px;
    padding: 40px;
    text-align: center;
}

/*new design*/

.selected{

}

.body_table{
    width: 100%;
    height: 100%;
    border-spacing: 0;
}

/*top*/

.b_top_left{
    width: 54px;
    background-image: url(../img/top_left.png);
    background-repeat: no-repeat;
}

.b_top_right{
    width: 54px;
    background-image: url(../img/top_right.png);
    background-repeat: no-repeat;
}

.b_top_center{
    height: 54px;
    background-image: url(../img/top.png);
    background-repeat: repeat-x;
}

/*bottom*/

.b_bottom_left{
    width: 54px;
    background-image: url(../img/bottom_left.png);
    background-repeat: no-repeat;
}

.b_bottom_right{
    width: 54px;
    background-image: url(../img/bottom_right.png);
    background-repeat: no-repeat;
}

.b_bottom_center{
    background-image: url(../img/bottom.png);
    background-repeat: repeat-x;
}

/*center*/

.b_center_left{
    width: 54px;
    background-image: url(../img/left.png);
    background-repeat: repeat-y;
}

.b_center_right{
    width: 54px;
    background-image: url(../img/right.png);
    background-repeat: repeat-y;
}

#data{
    width: 100%;
    height: 100%;    
    min-height: 250px;
    overflow: auto;
}

#wgt_name{
    height: 44px;
    margin: 10px 10px 0 10px;
    padding: 0;
    float: left;
    font-family: sans-serif;
    font-size: 24px;
    color: #7F613F;
    text-shadow: #FFDCA9 0 1px 0;
}

#wgt_reload, #wgt_edit, #wgt_display, #wgt_help{
    cursor: pointer;
    margin: 10px 10px 0 0;
    float: right;
    font-family: sans-serif;
    font-size: 24px;    
    line-height: 32px;
}

#wgt_display{
    padding-left: 35px;
    background: url(../img/toolbar-edit.png) left -32px no-repeat;
    color: white;
    display: none;
}

#wgt_edit{    
    padding-left: 35px;
    background: url(../img/slate-toolbar-edit.png) left top no-repeat;
    color: #7F613F;
    text-shadow: #FFDCA9 0 1px 0;
}

#wgt_reload{
    padding-left: 35px;
    background: url(../img/slate-toolbar-reload.png) left top no-repeat;
    color: #7F613F;
    text-shadow: #FFDCA9 0 1px 0;
}

.btl_pad{
    background-image: url(../img/pad-top-left.png) !important;
}

.btc_pad{
    background-image: url(../img/pad-top.png) !important;
}

.btr_pad{
    background-image: url(../img/pad-top-right.png) !important;
}

.bcl_pad{
    background-image: url(../img/pad-left.png) !important;
}

.bcr_pad{
    background-image: url(../img/pad-right.png) !important;
}

.bbl_pad{
    background-image: url(../img/pad-bottom-left.png) !important;
}

.bbc_pad{
    background-image: url(../img/pad-bottom.png) !important;
}

.bbr_pad{
    background-image: url(../img/pad-bottom-right.png) !important;
}

.without_radius{
    border-radius: 0 !important;
}

.without_back{
    background: none !important;
}

.pad_color{
    color: #FC9 !important;
    text-shadow: none !important;
}

.pad_reload{
    background: url(../img/toolbar-reload.png) left top no-repeat !important;
}

.pad_edit{
    background: url(../img/toolbar-edit.png) left top no-repeat !important;    
}

.pad_help{
    background: url(../img/toolbar-help.png) left top no-repeat !important;
}

.help_wood{
    background: url(../img/slate-toolbar-help.png) left -32px no-repeat !important;
    color: white !important;
    text-shadow: #7F613F 0 -1px 0 !important;
}

.help_pad{
    background: url(../img/toolbar-help.png) left -32px no-repeat !important;
    color: white !important;
}

#wgt_help{
    padding-left: 35px;
    background: url(../img/slate-toolbar-help.png) left top no-repeat;
    color: #7F613F;
    text-shadow: #FFDCA9 0 1px 0;
}

#help{
    height: 100%;    
    overflow: auto;
    display: none;
    background-color: #ccc;
    padding: 5px;
}

.open{

}

#parameters{    
    display: none;
    padding: 10px 20px;
    background: url("../img/parameters-bg.png");
    border-radius: 4px 4px 0 0;
}

.inline{
    display: inline-block;
    font-family: sans-serif;
    font-size: 14px;
    color: #666;
}

#parameters label {
    font-style: italic;
}

#style_select{   
    margin-left: 10px;
}

.display_wood{
    background: url(../img/slate-toolbar-edit.png) left -32px no-repeat !important;
    text-shadow: #7F613F 0 -1px 0;
}

.radius_ft{
    border-radius: 45px !important;
}