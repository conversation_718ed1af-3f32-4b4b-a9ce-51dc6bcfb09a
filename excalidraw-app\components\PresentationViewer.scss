.presentation-viewer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.presentation-viewer {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  width: 90vw;
  height: 85vh;
  max-width: 1200px;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .presentation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
    background: #f8f9fa;
    
    .header-left h3 {
      margin: 0;
      color: #333;
      font-size: 18px;
      font-weight: 600;
    }
    
    .header-controls {
      display: flex;
      gap: 8px;
      
      .view-mode-btn,
      .close-btn {
        background: none;
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 8px 12px;
        cursor: pointer;
        font-size: 16px;
        transition: all 0.2s ease;
        
        &:hover {
          background: #f0f0f0;
          border-color: #ccc;
        }
      }
      
      .close-btn {
        color: #666;
        font-weight: bold;
        
        &:hover {
          background: #ff4444;
          color: white;
          border-color: #ff4444;
        }
      }
    }
  }
  
  .url-input-section {
    padding: 16px 20px;
    border-bottom: 1px solid #e0e0e0;
    
    .url-input {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e0e0e0;
      border-radius: 8px;
      font-size: 14px;
      transition: border-color 0.2s ease;
      
      &:focus {
        outline: none;
        border-color: #4285f4;
        box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
      }
      
      &::placeholder {
        color: #999;
      }
    }
    
    .url-help {
      margin-top: 8px;
      font-size: 12px;
      color: #666;
    }
  }
  
  .slide-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 12px 20px;
    border-bottom: 1px solid #e0e0e0;
    background: #fafafa;
    
    .nav-btn {
      background: #4285f4;
      color: white;
      border: none;
      border-radius: 6px;
      padding: 8px 16px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: background-color 0.2s ease;
      
      &:hover:not(:disabled) {
        background: #3367d6;
      }
      
      &:disabled {
        background: #ccc;
        cursor: not-allowed;
      }
    }
    
    .slide-info {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      color: #333;
      
      .slide-input {
        width: 60px;
        padding: 4px 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        text-align: center;
        font-size: 14px;
      }
    }
    
    .slide-controls-right {
      margin-left: auto;
      
      .slide-slider {
        width: 200px;
        height: 6px;
        border-radius: 3px;
        background: #ddd;
        outline: none;
        cursor: pointer;
        
        &::-webkit-slider-thumb {
          appearance: none;
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: #4285f4;
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        &::-moz-range-thumb {
          width: 18px;
          height: 18px;
          border-radius: 50%;
          background: #4285f4;
          cursor: pointer;
          border: none;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
      }
    }
  }
  
  .iframe-container {
    flex: 1;
    position: relative;
    
    .slides-iframe {
      border: none;
      background: white;
    }
  }
  
  .error-message {
    padding: 40px 20px;
    text-align: center;
    color: #d93025;
    font-size: 16px;
    background: #fef7f0;
    border: 1px solid #fce8e6;
    margin: 20px;
    border-radius: 8px;
  }
  
  .placeholder {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    
    .placeholder-content {
      text-align: center;
      max-width: 500px;
      
      h4 {
        color: #333;
        margin-bottom: 20px;
        font-size: 20px;
      }
      
      ol {
        text-align: left;
        color: #666;
        line-height: 1.6;
        margin-bottom: 24px;
        
        li {
          margin-bottom: 8px;
        }
      }
      
      .example-url {
        background: #f8f9fa;
        padding: 16px;
        border-radius: 8px;
        border-left: 4px solid #4285f4;
        text-align: left;
        
        strong {
          color: #333;
        }
        
        code {
          display: block;
          margin-top: 8px;
          padding: 8px;
          background: #e8f0fe;
          border-radius: 4px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 12px;
          color: #1a73e8;
          word-break: break-all;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .presentation-viewer {
    width: 95vw;
    height: 90vh;
    
    .slide-controls {
      flex-wrap: wrap;
      gap: 8px;
      
      .slide-controls-right {
        margin-left: 0;
        width: 100%;
        
        .slide-slider {
          width: 100%;
        }
      }
    }
    
    .url-input-section .url-input {
      font-size: 16px; // Prevent zoom on iOS
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .presentation-viewer {
    background: #2d2d2d;
    color: white;
    
    .presentation-header {
      background: #3d3d3d;
      border-bottom-color: #555;
      
      h3 {
        color: white;
      }
    }
    
    .url-input-section {
      border-bottom-color: #555;
      
      .url-input {
        background: #3d3d3d;
        border-color: #555;
        color: white;
        
        &::placeholder {
          color: #aaa;
        }
      }
    }
    
    .slide-controls {
      background: #3d3d3d;
      border-bottom-color: #555;
    }
    
    .placeholder-content {
      .example-url {
        background: #3d3d3d;
        
        code {
          background: #4d4d4d;
          color: #87ceeb;
        }
      }
    }
  }
}
