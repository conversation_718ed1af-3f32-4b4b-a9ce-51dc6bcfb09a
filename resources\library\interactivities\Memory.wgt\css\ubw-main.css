html, body {
    margin:0;
    padding: 0;
    font-family: "helvetica neue";
    font-size: 14px;
    color: #666666;
}

#ubwidget {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: url("images/bg.png");
}

.theme-pad #ubwidget {
    border-radius: 40px;
    border-width: 52px;

    -webkit-border-image: url("images/pad-bd.png") 52 repeat;
    -moz-border-image: url("images/pad-bd.png") 52 repeat;
    border-image: url("images/pad-bd.png") 52 repeat;
}

.theme-slate #ubwidget {
    border-radius: 44px;
    border-width: 52px;

    -webkit-border-image: url("images/slate-bd.png") 52 repeat;
    -moz-border-image: url("images/slate-bd.png") 52 repeat;
    border-image: url("images/slate-bd.png") 52 repeat;
}

#ubwidget > .wrapper {
    position: absolute;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
    overflow: hidden;
}


.theme-slate #ubwidget > .wrapper, .theme-pad #ubwidget > .wrapper {
    position: absolute;
    /* top: -49px;
    bottom: -5px;
    left: -5px;
    right: -5px; */
    overflow: hidden;
}

#toolbar {
    display: table;
    width: 100%;
    height: 44px;
    font-size: 24px;
    color: #FFCC99;
    padding:0 10px;
}

.theme-pad #toolbar, .theme-slate #toolbar {
    height: 38px;
    padding: 6px 0 0;
}

.theme-slate #toolbar {
    color: #7F613F;
    text-shadow: #FFDCA9 0 1px 0;
}


#toolbar > * {
    display: table-cell;
    height: 100%;
    vertical-align: middle;
}

#toolbar .actions {
    text-align: right;
}

#toolbar button, h1 {
    font-weight: normal;
    font-size: 24px;
    color: #FFCC99;
    margin: 0;
}

.theme-slate #toolbar button, .theme-slate h1 {
    color: #7F613F;
    text-shadow: #FFDCA9 0 1px 0;
}

#toolbar button {
    border: none;
    padding: none;
    outline: none;
    background: none;

    cursor: pointer;
    padding-left: 34px;
    margin-left: 10px;
    height: 32px;
}

#toolbar button span {
    display: block;
    line-height: 32px;
}

#toolbar button[role='edit'] {
    background: url("images/toolbar-edit.png") left top no-repeat;
}

#toolbar button[role='view'] {
    display: none;
    color: #FFF;
    background: url("images/toolbar-edit.png") left -32px no-repeat;
}

#toolbar button[role='reload'] {
    background: url("images/toolbar-reload.png") left top no-repeat;
}

#toolbar button[role='help'] {
    background: url("images/toolbar-help.png") left top no-repeat;
    display: none;
}

.hasHelp #toolbar button[role='help'] {
    display: inline-block;
}

.showHelp #toolbar button[role='help'] {
    color: #FFF;
    background-position: left -32px;
}

.theme-slate #toolbar button[role='edit'] {
    background-image: url("images/slate-toolbar-edit.png");
}

.theme-slate #toolbar button[role='view'] {
    text-shadow: #7F613F 0 -1px 0;
    background: url("images/slate-toolbar-edit.png") left -32px no-repeat;
}

.theme-slate #toolbar button[role='reload'] {
    background-image: url("images/slate-toolbar-reload.png");
}

.theme-slate #toolbar button[role='help'] {
    background-image: url("images/slate-toolbar-help.png");
}

.showHelp.theme-slate #toolbar button[role='help'] {
    text-shadow: #7F613F 0 -1px 0;
}


.onEdit #toolbar button[role='view'] {
    display: inline-block;
}

.onEdit #toolbar button[role='edit'] {
    display: none;
}

#help {
    width: 300px;
    height: 400px;
    position: absolute;
    margin-top: 10px;
    right: 10px;
    z-index: 10000;
    display: none;
}

.showHelp #help {
    display: block;
}

#content {
    position: absolute;
    top: 44px;
    bottom: 0;
    overflow: auto;
    left: 0;
    right: 0;

    background-image: -moz-radial-gradient(center center, ellipse closest-side, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0) 100%);
    background-image: -webkit-radial-gradient(center center, ellipse closest-side, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0) 100%);
    background-image: -o-radial-gradient(center center, ellipse closest-side, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0) 100%);
    background-image: -ms-radial-gradient(center center, ellipse closest-side, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0) 100%);
    background-image: radial-gradient(center center, ellipse closest-side, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0) 100%);
}

#parameters {
    display:none;
    padding: 10px 20px;
    background: url("images/parameters-bg.png");

    -webkit-border-radius: 4px 4px 0 0;
    -moz-border-radius: 4px 4px 0 0;
    -mz-border-radius: 4px 4px 0 0;
    border-radius: 4px 4px 0 0;
}

#parameters label {
    font-style: italic;
}

#parameters  label > select, #parameters  label > input{
    margin-left: 10px;
}

#parameters > div.inline {
    display: inline-block;
}

#parameters > div.inline+div.inline {
    margin-left: 20px;
}

#parameters input[type=text],
#parameters input[type=number] {
    height: 26px;
    border: 1px solid #BBB;
    background-color: #FFF;

    padding: 0 4px;

    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    border-radius: 4px;

    -webkit-box-shadow: 0 1px 0 #FFF;
    -moz-box-shadow: 0 1px 0 #FFF;
    -ms-box-shadow: 0 1px 0 #FFF;
    box-shadow: 0 1px 0 #FFF;
}

#parameters input.tiny {
    width: 40px;
}

#parameters input.small {
    width: 80px;
}

#parameters input.medium {
    width: 120px;
}

#parameters input.long {
    width: 160px;
}

#scene {
    padding: 20px;
}

#scene > * {
    margin: 0 auto;
}
.onEdit #scene {
}

.onEdit #parameters {
    display: block;
}

/*
.card-container {
        -webkit-perspective: 600px;
        width:100%;
        height: 100%;
}
.card {
        position: relative;
        width:100%;
        height: 100%;
}
.card > div {
        position: absolute;
        width:100%;
        height: 100%;
        -webkit-transform-style: preserve-3d;
        -webkit-transition: all .5s ease-in-out;
        -webkit-backface-visibility: hidden;
}
.card > div:first-child {
        -webkit-transform: rotateY( 0deg );
}
.card > div:last-child {
        -webkit-transform: rotateY( -180deg );
}
.card.flip > div:first-child {
        -webkit-transform: rotateY( 180deg );
}
.card.flip > div:last-child {
        -webkit-transform: rotateY( 0deg );
}
*/
