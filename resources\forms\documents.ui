<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>documents</class>
 <widget class="QWidget" name="documents">
  <property name="enabled">
   <bool>true</bool>
  </property>
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>796</width>
    <height>646</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="contextMenuPolicy">
   <enum>Qt::DefaultContextMenu</enum>
  </property>
  <property name="windowTitle">
   <string>OpenBoard Documents</string>
  </property>
  <property name="styleSheet">
   <string notr="true"/>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="0" column="0">
    <widget class="QWidget" name="topWidget" native="true">
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <widget class="QSplitter" name="splitter">
        <property name="minimumSize">
         <size>
          <width>400</width>
          <height>0</height>
         </size>
        </property>
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="opaqueResize">
         <bool>false</bool>
        </property>
        <property name="handleWidth">
         <number>6</number>
        </property>
        <property name="childrenCollapsible">
         <bool>false</bool>
        </property>
        <widget class="QWidget" name="topLeftWidget" native="true">
         <property name="sizePolicy">
          <sizepolicy hsizetype="Expanding" vsizetype="Preferred">
           <horstretch>0</horstretch>
           <verstretch>0</verstretch>
          </sizepolicy>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_2">
          <item>
           <layout class="QHBoxLayout" name="horizontalLayout_3">
            <property name="sizeConstraint">
             <enum>QLayout::SetMinimumSize</enum>
            </property>
            <item>
             <widget class="QComboBox" name="sortKind">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
                <horstretch>4</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="maximumSize">
               <size>
                <width>200</width>
                <height>16777215</height>
               </size>
              </property>
              <property name="styleSheet">
               <string notr="true"/>
              </property>
              <property name="minimumContentsLength">
               <number>0</number>
              </property>
              <item>
               <property name="text">
                <string>Creation date</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>Update date</string>
               </property>
              </item>
              <item>
               <property name="text">
                <string>Alphabetical order</string>
               </property>
              </item>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeType">
               <enum>QSizePolicy::Fixed</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>10</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
            <item>
             <widget class="QToolButton" name="sortOrder">
              <property name="sizePolicy">
               <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                <horstretch>0</horstretch>
                <verstretch>0</verstretch>
               </sizepolicy>
              </property>
              <property name="toolTip">
               <string>Sort Order</string>
              </property>
              <property name="styleSheet">
               <string notr="true">QToolButton { border-style:none; border-width: 0px;margin-left:2px;margin-right:2px}</string>
              </property>
              <property name="text">
               <string notr="true"/>
              </property>
              <property name="icon">
               <iconset resource="../OpenBoard.qrc">
                <normaloff>:/images/asc.png</normaloff>
                <normalon>:/images/desc.png</normalon>:/images/asc.png</iconset>
              </property>
              <property name="checkable">
               <bool>true</bool>
              </property>
             </widget>
            </item>
            <item>
             <spacer name="horizontalSpacer_2">
              <property name="orientation">
               <enum>Qt::Horizontal</enum>
              </property>
              <property name="sizeHint" stdset="0">
               <size>
                <width>40</width>
                <height>20</height>
               </size>
              </property>
             </spacer>
            </item>
           </layout>
          </item>
          <item>
           <widget class="UBDocumentTreeView" name="documentTreeView">
            <property name="minimumSize">
             <size>
              <width>0</width>
              <height>0</height>
             </size>
            </property>
            <property name="maximumSize">
             <size>
              <width>16777215</width>
              <height>16777215</height>
             </size>
            </property>
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <property name="autoScroll">
             <bool>true</bool>
            </property>
            <property name="autoScrollMargin">
             <number>65</number>
            </property>
            <property name="selectionMode">
             <enum>QAbstractItemView::ExtendedSelection</enum>
            </property>
            <property name="selectionBehavior">
             <enum>QAbstractItemView::SelectRows</enum>
            </property>
            <property name="verticalScrollMode">
             <enum>QAbstractItemView::ScrollPerItem</enum>
            </property>
            <property name="animated">
             <bool>true</bool>
            </property>
            <property name="headerHidden">
             <bool>true</bool>
            </property>
            <attribute name="headerVisible">
             <bool>false</bool>
            </attribute>
           </widget>
          </item>
         </layout>
        </widget>
        <widget class="QFrame" name="topRightFrame">
         <property name="styleSheet">
          <string notr="true"/>
         </property>
         <layout class="QVBoxLayout" name="verticalLayout_3">
          <item>
           <widget class="UBDocumentThumbnailWidget" name="thumbnailWidget">
            <property name="contextMenuPolicy">
             <enum>Qt::NoContextMenu</enum>
            </property>
            <property name="styleSheet">
             <string notr="true"/>
            </property>
            <property name="frameShape">
             <enum>QFrame::NoFrame</enum>
            </property>
            <property name="frameShadow">
             <enum>QFrame::Plain</enum>
            </property>
            <property name="lineWidth">
             <number>1</number>
            </property>
            <property name="verticalScrollBarPolicy">
             <enum>Qt::ScrollBarAsNeeded</enum>
            </property>
            <property name="horizontalScrollBarPolicy">
             <enum>Qt::ScrollBarAlwaysOff</enum>
            </property>
            <property name="dragMode">
             <enum>QGraphicsView::NoDrag</enum>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QWidget" name="bottomRightFrame" native="true">
            <layout class="QHBoxLayout" name="horizontalLayout_2">
             <property name="spacing">
              <number>0</number>
             </property>
             <property name="leftMargin">
              <number>0</number>
             </property>
             <property name="topMargin">
              <number>0</number>
             </property>
             <property name="rightMargin">
              <number>0</number>
             </property>
             <property name="bottomMargin">
              <number>0</number>
             </property>
             <item>
              <spacer name="horizontalSpacer2">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>300</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
             <item>
              <widget class="QSlider" name="documentZoomSlider">
               <property name="sizePolicy">
                <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
                 <horstretch>0</horstretch>
                 <verstretch>0</verstretch>
                </sizepolicy>
               </property>
               <property name="minimumSize">
                <size>
                 <width>150</width>
                 <height>0</height>
                </size>
               </property>
               <property name="styleSheet">
                <string notr="true"/>
               </property>
               <property name="minimum">
                <number>50</number>
               </property>
               <property name="maximum">
                <number>500</number>
               </property>
               <property name="singleStep">
                <number>10</number>
               </property>
               <property name="value">
                <number>150</number>
               </property>
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="invertedAppearance">
                <bool>false</bool>
               </property>
               <property name="invertedControls">
                <bool>false</bool>
               </property>
              </widget>
             </item>
             <item>
              <spacer name="horizontalSpacer1">
               <property name="orientation">
                <enum>Qt::Horizontal</enum>
               </property>
               <property name="sizeType">
                <enum>QSizePolicy::Maximum</enum>
               </property>
               <property name="sizeHint" stdset="0">
                <size>
                 <width>40</width>
                 <height>20</height>
                </size>
               </property>
              </spacer>
             </item>
            </layout>
           </widget>
          </item>
         </layout>
        </widget>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="0" margin="0"/>
 <customwidgets>
  <customwidget>
   <class>UBDocumentThumbnailWidget</class>
   <extends>QGraphicsView</extends>
   <header>gui/UBDocumentThumbnailWidget.h</header>
  </customwidget>
  <customwidget>
   <class>UBDocumentTreeView</class>
   <extends>QTreeView</extends>
   <header>document/UBDocumentController.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../OpenBoard.qrc"/>
 </resources>
 <connections/>
</ui>
