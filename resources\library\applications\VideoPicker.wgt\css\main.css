
body{
	margin: 0;
    font-family:<PERSON><PERSON>, Sans-serif;
    font-size:15px;
    color:#444;
}

.icon-container{
    width:40px;
    height:40px;
    overflow:hidden;
}

#shadow{
    position:absolute;
    z-index:0;
    width:352px;
    height:58px;
    padding-left:8px;
    margin-top:3px;
    margin-left:3px;
    background-color:rgb(220,220,220);
}

#inputfield{
    position:absolute;
    z-index:1;
    width:360px;
    height:58px;
    background-color:rgb(230,230,233);
    border:1px solid rgb(220, 220, 220);
}

.button{
    float:left;
    padding:4px 5px;
    cursor:pointer;
    background-color:rgb(45,65,250);
    border:1px solid;
    border-color:rgb(45,65,250) rgb(25,45,230) rgb(25,45,230) rgb(45,65,250);
    text-transform:capitalize;
    font-size:12px;
    font-weight:bold;
    color:white;
}

#search-button{
    margin-top:17px;
    margin-left:17px;
}

#search-button.over{
    border-color:rgb(25,45,230) rgb(45,65,250) rgb(45,65,250) rgb(25,45,230);
}

#show-inputfield-button{
    margin-left:12px;
}

#container{
    float:left;
    position:relative;
    overflow:visible; 
    height:auto;
}

#container.welcome{
    width:405px;
    margin-top:87px;
    background-color:#FFF;
}

#container.load{
    margin-top:0px;
}

#container.show{
    margin-top:0px;
    height:auto;
}

#container.change{
    margin-top:80px;
    height:auto;
}

#container.error{
    width:400px;
    height:auto;
    margin-top:87px;
    background-color:#FFF;
    position:relative;
}

#arrow{
    text-align:center;
    width:100%;
    margin-top:-36px;
    margin-bottom:-1px;
    position:relative;
    z-index:4;
}

#embeded-content{
    position:relative;
    z-index:3;
    float:left;
    height:auto;
}

#show-container{
    overflow:auto;
    height:auto;
}

#show-area{
    display:none;
    position:absolute;
    top:150px;
    left:400px;
    z-index:999;
}

#show-area .icon-container{
    margin:0 auto;
}

#textbox{
    width:245px; 
    float:left;
    padding:2px;
    margin-top:17px;
    margin-left:17px;
    outline:none;
    font-family:Arial, Sans-serif;
    font-size:15px;
    color:#444;
}

#container-shadow{
    width:100%;
    height:30px;
}

#container-shadow td{
    height:100%;
}

#shadow-left{
    width:193px;
    background-image:url(../imgs/left.png);
    background-repeat:no-repeat;
}

#shadow-center{
    width:auto;
    background-image:url(../imgs/center.png);
    background-repeat:repeat;
}

#shadow-right{
    width:193px;
    background-image:url(../imgs/right.png);
    background-repeat:no-repeat;
}

.bordered1{
    border: 1px solid red;
}

.bordered2{
    border:1px solid #666;
}