---
name: <PERSON>ug report
about: Create a report to help us improve
title: "[<PERSON><PERSON>]"
labels: 'bug'
assignees: ''

---

### Describe the bug
<!---
A clear and concise description of what the bug is.
-->

### To Reproduce
<!---
This is the most important step!! The more minimal and precise your steps to reproduce the bug are, the greater the chances of seeing the bug fixed quickly will be.
The use of numbered list as shown below is MANDATORY. If not used, issue will be closed as incomplete
-->

1. Go to '...'
2. Click on '....'
3. Scroll down to '....'

#### Expected behavior
A clear and concise description of what you expected to happen.

#### Actual behavior
*A clear and concise description of what actually happened*

### Screenshots
If applicable, add screenshots to help explain your problem.

### Additional resources
*PDF, images, sounds... anything useful to reproduce the bug*

### Context
<!---
This part is MANDATORY (if not completed, the issue will be closed as incomplete)
-->
  - reproducibility : `systematic` `not systematic`
  - OpenBoard version :
      - bug happens with : `1.6.4` `1.7.0` `1.7.1a-x.y.z`
      - bug doesn't happen with : `1.6.4` `1.7.0` `1.7.1a-x.y.z`
      - didn't check : `1.6.4` `1.7.0` `1.7.1a-x.y.z`
  - OS : 
      - bug happens with : `Windows` `Linux` `macOS`
      - bug doesn't happen with : `Windows` `Linux` `macOS`
      - didn't check : `Windows` `Linux` `macOS` 
  - Installation source : `OpenBoard download page` `Linux distribution repository`, `own build` `flatpak` `AppImage`
  - If you built OpenBoard from source, please provide the branch you're working on : `master`
  
### Additional context
<!---
Additional context is very useful for an application like OpenBoard, that can work with a Wacom, an interactive whiteboard or a simple computer. Please provide any useful detail. 

OS detailed version is MANDATORY (if not provided, the issue will be closed as incomplete). 

For Linux users, simply copy/paste the result of the following command : lsb_release -d
For macOS users, go to "Apple > about this mac" and please provide (or take a screenshot and copy/paste it below) the OS version and device description, for example : "macOS Monterey 12.0.1 MacBook Pro (13 inches, M1, 2020)" 
For Windows users, please go to Start > Parameters > System > About and please provide or take a screenshot and copy/paste it below) information given in the page.

Here's an example of what is expected : 

- OS(es) detailed version (for the ones you reproduced the bug on) : 
    - Linux : `Description:	Ubuntu 20.04.6 LTS`
- number and resolution of monitors : `1`
- if used, manufacturer and model of graphic tablet input device : `Wacom Cintiq 16`
-->
- OS(es) detailed version (for the ones you reproduced the bug on) 
    - Linux : ` `
    - Windows : ` `
    - macOS : ` `
- number and resolution of monitors : ` `
- if used, manufacturer and model of graphic tablet input device : ` `
