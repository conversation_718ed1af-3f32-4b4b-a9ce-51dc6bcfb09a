
.jScrollPaneContainer {
	position: relative;
	overflow: hidden;
	z-index: 1;
}

.jScrollPaneTrack {
	position: absolute;
	cursor: pointer;
	right: 0;
	top: 0;
	height: 100%;
	background: #aaa;
}
.jScrollPaneDrag {
	position: absolute;
	background: #666;
	cursor: pointer;
	overflow: hidden;
}
.jScrollPaneDragTop {
	position: absolute;
	top: 0;
	left: 0;
	overflow: hidden;
}
.jScrollPaneDragBottom {
	position: absolute;
	bottom: 0;
	left: 0;
	overflow: hidden;
}
a.jScrollArrowUp {
	display: block;
	position: absolute;
	z-index: 1;
	top: 0;
	right: 0;
	text-indent: -2000px;
	overflow: hidden;
	/*background-color: #666;*/
	height: 9px;
}
a.jScrollArrowUp:hover {
	/*background-color: #f60;*/
}

a.jScrollArrowDown {
	display: block;
	position: absolute;
	z-index: 1;
	bottom: 0;
	right: 0;
	text-indent: -2000px;
	overflow: hidden;
	/*background-color: #666;*/
	height: 9px;
}
a.jScrollArrowDown:hover {
	/*background-color: #f60;*/
}
a.jScrollActiveArrowButton, a.jScrollActiveArrowButton:hover {
	/*background-color: #f00;*/
}