.selected,
.result,
.input {
	color: #FFCC99;
}

#disc {
	position: relative;
	margin: 200px auto 0;

	color: #FFF;

	text-shadow: 0px 1px 2px #000;
}

#disc .circle {
	position: absolute;
	top: 50%;
	left: 50%;
	border-radius: 50%;
}

.result {
	width: 430px;
	height: 430px;

	margin-top: -215px;
	margin-left: -215px;

	/*OPEN SANKORE bug sur cette animation*/
	/*-webkit-transition: all 0.25s ease-in-out;*/
}

.hide {
	visibility: hidden;
}

.numbers {
	width: 300px;
	height: 300px;
	border: 4px solid #FFF;

	margin-top: -150px;
	margin-left: -150px;

	background-color: rgba(0,0,0,0.2);

}

.operators {
	width: 170px;
	height: 170px;
	border: 4px solid #FFF;

	margin-top: -85px;
	margin-left: -85px;

	background-color: rgba(0,0,0,0.2);
}

.input {
	width: 90px;
	height: 90px;
	border: 4px solid #FFF;

	margin-top: -45px;
	margin-left: -45px;

	background-color: rgba(0,0,0,0.2);
	box-shadow: inset 0px 0px 4px #000;
}

.result > div,
.numbers > div,
.operators > div {
	position: absolute;
	top: 0px;
	bottom: 50%;
	left: 50%;
	text-align: center;
	-webkit-transform-origin: 50% 100%;
}

.numbers > div {
	width: 40px;
	font-size: 32px;
	line-height: 32px;
	margin-left: -20px;
}

.operators > div {
	width: 20px;
	font-size: 28px;
	line-height: 20px;
	margin-left: -10px;
}

.result > div > div,
.numbers > div > div,
.operators > div > div {
	cursor: pointer;
}

.numbers > div > div {
	padding: 15px 0;
}

.operators > div > div {
	padding: 5px 0;
}

.input > div {
	padding: 0 8px;
	font-size: 40px;
	line-height: 90px;
	text-align: center; 
}

.result > div {
	width: 60px;
	font-size: 50px;
	line-height: 40px;
	margin-left: -30px;
	text-align: center;
}

#result {
	height: 40px;
}

#result > div {
	position: relative;
	overflow: hidden;
	height: 100%;
    width: 140px;
}
#result > div > div {
	position: absolute;
	width: 100%;
}

.reste {
	font-size: 24px;
}


#result > div > div:first-child {
	display: block;
}

#result > div > div:last-child {
	display: none;
}

#result.flip > div > div:first-child {
	display: none;
}

#result.flip > div > div:last-child {
	display: block;
}

/*OPEN SANKORE bug sur cette animation*/
/*
.csstransforms3d .result {
	-webkit-perspective: 600px;
}

.csstransforms3d #result > div > div {
	-webkit-transform-style: preserve-3d !important;
  	-webkit-transition: all .5s ease-in-out;
	-webkit-backface-visibility: hidden;	
}

.csstransforms3d #result > div > div:first-child {
	-webkit-transform: rotateY( 0deg );
}

.no-csstransforms3d #result > div > div:first-child {
	display: block;
}

.csstransforms3d #result > div > div:last-child {
	-webkit-transform: rotateY( -180deg );
}

.no-csstransforms3d #result > div > div:last-child {
	display: none;
}

.csstransforms3d #result.flip > div > div:first-child {
	-webkit-transform: rotateY( 180deg );
}

.no-csstransforms3d #result.flip > div > div:first-child {
	display: none;
}

.csstransforms3d #result.flip > div > div:last-child {
	-webkit-transform: rotateY( 0deg );
}

.no-csstransforms3d #result.flip > div > div:last-child {
	display: block;
}
*/