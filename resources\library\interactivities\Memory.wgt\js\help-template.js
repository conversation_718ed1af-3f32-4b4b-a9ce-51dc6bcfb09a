<h3> Memory </h3>
<h4>Mental calculation, matching game</h4>

<p>The goal of the App Memory is to make pairs and remembering the location of the various cards.</p>

<p>Cards are laid out in a grid face down. The player turns two cards. If the two cards match, cards are not covered. If they do not match, the cards are turned back over.</p>
<p>The game is over when all pairs have been found.</p>

<p>Cards are randomly arranged in the game mode.</p>
<p> "Reload" button resets the game. </p>


<p> Enter the "Edit" mode to : </p>
<ul> <li> choose the theme of interactivity : pad, slate or none (by default : pad),</li>
<li> choose the number of cards (4, 6 or 8 cards),</li>
<li> select the displayed time of returned cards,</li>
<li> edit a card (insert a picture or change the text).</li> </ul>

<p> Place pairs of cards in the same column (same figure in each card). </p>
<p> Cards have a default text field. </p>
<p> Click on the field to insert text. </p>
<p> Click on the box before dragging and dropping picture from your library. </p>

<p> "Display" button comes back to the activity.</p>