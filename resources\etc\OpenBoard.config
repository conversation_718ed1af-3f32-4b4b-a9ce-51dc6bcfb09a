[App]
HideCheckForSoftwareUpdate=false
HideSwapDisplayScreens=true
EnableAutomaticSoftwareUpdates=true
EnableSoftwareUpdates=true
EnableStartupHints=true
FavoriteToolURIs=openboardtool://openboard/mask, openboardtool://ruler, openboardtool://compass, openboardtool://protractor, openboardtool://triangle, openboardtool://magnifier, openboardtool://cache
IsInSoftwareUpdateProcess=false
LastSessionDocumentUUID=
LastSessionPageIndex=0
PageCacheSize=20
PreferredLanguage=fr_CH
ProductWebAddress=http://www.openboard.ch
RotationAngleStep=5.
RunInWindow=false
SoftwareUpdateURL=http://www.openboard.ch/update.json
StartMode=
SwapControlAndDisplayScreens=false
ToolBarDisplayText=true
ToolBarOrientationVertical=false
ToolBarPositionedAtTop=true
TutorialUrl=http://www.openboard.ch
UseMultiscreenMode=true
UseSystemOnScreenKeyboard=true

[Board]
AutoSaveIntervalInMinutes=3
CrossColorDarkBackground=#C8C0C0C0
CrossColorLightBackground=#A5E1FF
DarkBackground=0
DefaultPageSize=@Size(1280 960)
EraserCircleWidthIndex=1
FeatureSliderPosition=40
GridDarkBackgroundColors=#FFFFFF, #FF3400, #66C0FF, #81FF5C, #FFFF00, #B68360, #FF497E, #8D69FF, #C8C0C0C0
GridLightBackgroundColors=#000000, #FF0000, #004080, #008000, #FFDD00, #C87400, #800040, #008080, #5F2D0A, #A5E1FF
InterpolateMarkerStrokes=true
InterpolatePenStrokes=true
KeyboardPaletteKeyBtnSize=16x16
LeftLibPaletteBoardModeIsCollapsed=true
LeftLibPaletteBoardModeWidth=270
LeftLibPaletteDesktopModeIsCollapsed=true
LeftLibPaletteDesktopModeWidth=270
MagnifierDrawingMode=0
MarkerAlpha=0.5
MarkerColorIndex=0
MarkerDarkBackgroundColors=#FFFF00, #FF4400, #66C0FF, #81FF5C, #B68360, #FF497E, #8D69FF, #FFFFFF
MarkerDarkBackgroundSelectedColors=#FFFF00, #FF4400, #66C0FF, #81FF5C, #B68360
MarkerFineWidth=12
MarkerLightBackgroundColors=#E3FF00, #FF0000, #004080, #008000, #C87400, #800040, #008080, #000000
MarkerLightBackgroundSelectedColors=#E3FF00, #FF0000, #004080, #008000, #C87400
MarkerMediumWidth=24
MarkerPressureSensitive=false
MarkerStrongWidth=48
pageDpi=0
PenColorIndex=0
PenDarkBackgroundColors=#FFFFFF, #FF3400, #66C0FF, #81FF5C, #FFFF00, #B68360, #FF497E, #8D69FF, #000000
PenDarkBackgroundSelectedColors=#FFFFFF, #FF3400, #66C0FF, #81FF5C, #FFFF00
PenFineWidth=1.5
PenLightBackgroundColors=#000000, #FF0000, #004080, #008000, #FFDD00, #C87400, #800040, #008080, #5F2D0A, #FFFFFF
PenLightBackgroundSelectedColors=#000000, #FF0000, #004080, #008000, #FFDD00
PenLineWidthIndex=0
PenMediumWidth=3
PenPressureSensitive=true
PenStrongWidth=8
PointerDiameter=40
RightLibPaletteBoardModeIsCollapsed=true
RightLibPaletteBoardModeWidth=270
RightLibPaletteDesktopModeIsCollapsed=true
RightLibPaletteDesktopModeWidth=270
SeyesRuledBackground=false
ShowEraserPreviewCircle=true
ShowMarkerPreviewCircle=true
ShowPenPreviewCircle=true
PenPreviewFromSize=5
ShowToolsPalette=false
SimplifyMarkerStrokes=true
SimplifyPenStrokes=true
SimplifyPenStrokesThresholdAngle=3
SimplifyPenStrokesThresholdWidthDifference=2
StartupKeyboardLocale=0
UseHighResTabletEvent=true
ZoomBase=1.0005
ZoomFactor=1.4099999999999999

[Community]
CredentialsPersistence=false
Password=
Username=

[Document]
EmptyGroupNames=@Invalid()
emptyTrashForOlderDocuments=false
emptyTrashDaysValue=30
ThumbnailWidth=150
SortKind=0
SortOrder=0
SplitterLeftSize=200
SplitterRightSize=800
ShowDateColumnOnAlphabeticalSort=false

[IntranetPodcast]
Author=
PublishToIntranet=false
PublishingUrl=

[Library]
AnimationsDirectory=./library/animations
ApplicationsDirectory=./library/applications
AudiosDirectory=./library/audios
ImageDirectory=./library/pictures
ImageThumbnailWidth=150
InteractivitiesDirectory=./library/interactivities
LibIconSize=80
ShapeDirectory=./library/shape
ShapeThumbnailWidth=50
ShowDetailsForLocalItems=false
SoundThumbnailWidth=50
VideoThumbnailWidth=80
VideosDirectory=./library/videos

[Mirroring]
RefreshRateInFramePerSecond=2

[PDF]
ExportBackgroundGrid=false
ExportBackgroundColor=false
Margin=20
PageFormat=A4
Resolution=300
UsePDFMerger=true

[Podcast]
AudioRecordingDevice=Default
FramesPerSecond=10
PublishToYouTube=false
QuickTimeQuality=High
VideoSize=Medium
WindowsMediaBitsPerSecond=1700000

[SVG]
ViewBoxMargin=50

[Web]
AlternativeUserAgent="Mozilla/5.0 (%1; %2; rv:91.0) Gecko/20100101 Firefox/91.0"
AlternativeUserAgentDomains=google.*
CookieAutoDelete=false
CookieKeepDomains=
CookiePolicy=DenyThirdParty
HistoryLimit=15
Homepage=http://www.openboard.ch
PrivateBrowsing=false
SearchEngineUrl=https://www.qwant.com/?q=%1
ShowPageImediatelyOnMirroredScreen=false
UseExternalBrowser=false

[YouTube]
CredentialsPersistence=false
UserEMail=
