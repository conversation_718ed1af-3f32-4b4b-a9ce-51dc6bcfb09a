/* Style pour les menus */
.menu{
	position: absolute;
	top: 0%;
	width: 100%;
	max-width: 800px;
	min-height: 411px;
	max-height: 100%;
	overflow: auto;
	background-color: rgba(255, 255, 255, 0.7);
	border-style: none;
	border-width: 1px;
	border-color: rgba(0,70,128, 0.9);
	border-radius: 4px;
	-webkit-border-radius: 4px;
	z-index: 2;
	display: none;
	box-sizing: border-box;
}
.barreBasMenu{
	position: absolute;
	bottom: 0px;
/* 	right: 0px; */
}
.barreBasMenu > div{
	position: fixed;
	width: 100%;
	max-width: 800px;
	box-sizing: border-box;
	/*    right: 0px; */
/* 	bottom: 14px; */
}
.barreBasMenu > div > div{
	position: absolute;
	bottom: 0px;
	right: 0px;
	width: 100%;
	box-sizing: border-box;
	padding-right: 28px;
/* 	background-color: rgba(255,0,0,0.3); */
	text-align: right;
}
.contenuMenu{
	font-size: 14px;
	padding: 15px;
	padding-top: 40px;
/* 	overflow: auto; */
/* 	height: 100%; */
/* 	position: absolute; */
/* 	top: 0px; */
/* 	bottom: 0px; */
}
.avecBordures{
/* 	border-top: 1px solid rgba(255, 255, 255, 0.4); */
/* 	height: 80% */
}
.ongletMenu{
	position: fixed;
/* 	top: 0px; */
	width: 100%;
	max-width: 800px;
/* 	height: 50px; */
/* 	background-color: rgba(255,0,0,0.3); */
/* 	border-spacing: 15px; */
	box-sizing: border-box;
	padding-right: 14px;
	text-align: center;
	z-index: 1;
}
.ongletMenu > div{
/* 	position: fixed; */
/* 	width: inherit; */
/* 	top: 0px; */
/* 	left: 0px; */
/* 	right: 0px; */
}
.deuxOnglets span{
	width: 44%;
}
.troisOnglets span{
	width: 28%;
}
.ongletMenu span{
	display: inline-block;
	min-width: 150px;
	padding: 5px;
	margin: 0px 7px;
	text-align: center;
	font-weight: normal;
	background-color: rgba(255, 255, 255, 0.8);
	background-image: url('../Images/gradient3.png');
	border: 1px solid rgba(255, 255, 255, 0.9);
	border-top: none;
	border-bottom-left-radius: 6px;
	border-bottom-right-radius: 6px;
	-webkit-border-bottom-left-radius: 6px;
	-webkit-border-bottom-right-radius: 6px;
	box-shadow: 0px 0px 3px rgba(0,0,0,0.5);
	opacity: 0.7;
	cursor: pointer;
	-webkit-user-select: none;
	-khtml-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
}
.ongletMenu span.ongletMenuActuel{
	opacity: 0.9;
	font-weight: bold;
	background-color: rgba(255, 255, 255, 0.9);
}
.ongletMenu span:hover{
	opacity: 1;
	box-shadow:0px 0px 3px rgba(0,0,0,0.5), 0px 0px 7px rgba(255,255,255,1);
}
.ongletMenu span:active{
	opacity: 0.7;
	font-weight: bold;
	box-shadow:0px 0px 3px rgba(0,0,0,0.5), 0px 0px 7px rgba(255,255,255,0.5);
}
.menu table.colonnes tr td{
	vertical-align: top;
}
.menu table.colonnes tr td{
	border-left: 1px solid rgba(255,255,255,0.5);
	padding-left: 4px;
}
.menu table.colonnes tr td.premiereColonne{
	border: none;
	padding-right: 4px;
}


.miniMenu{
	position: absolute;
	top: 45%;
	left: 50%;
	margin-left: -160px;
	margin-top: -120px;
	width: 320px;
	min-height: 240px;
	padding: 5px;
	text-align: center;
	font-size: 14px;
	overflow: auto;
	background-color: rgba(255, 255, 255, 0.6);
	background-image: url('../Images/gradient2.png');
	background-size: 100% 100%;
	box-shadow: 0px 0px 5px rgba(0,0,0,0.5);
	border-style: solid;
	border-width: 1px;
	border-color: rgba(255,255,255, 0.5);
	border-radius: 8px;
	-webkit-border-radius: 8px;
	z-index: 2;
	display: none;
}
.alertMenu{
	position: absolute;
	top: 126px;
	left: 206px;
	width: 220px;
	height: 140px;
	padding: 10px;
	text-align: center;
	font-size: 14px;
	overflow: auto;
	background-color: rgba(255, 255, 255, 0.9);
	background-image: url('../Images/gradient2.png');
	background-size: 100% 100%;
	border-style: solid;
	border-width: 1px;
	border-color: rgba(0,70,128, 0.9);
	border-radius: 15px;
	-webkit-border-radius: 15px;
	z-index: 3;
	display: none;
}
.miniMenu select{
/* 	width: 65px; */
}
.miniMenu input{
	width: 45%;
	height: 32px;
}
.miniMenu input.smallInput{
	width: 45%;
	height: 16px;
}
.alertMenu input{
	width: 45%;
	height: 32px;
}


/* Menu "à propos" */
#credits{
	width: 80%;
	margin: auto;
	margin-top: 10px;
}
#credits tr td{
	padding: 10px;
	width: 100%;
	height: 150px;
	text-align: center;
	text-shadow: 2px 2px 6px rgba(255,255,255,1);
	vertical-align: middle;
	background-color: rgba(255,220,150,0.5);
	background-image: url('../Images/gradient2.png');
	background-size: 100% 100%;
	border: 1px solid rgba(255,230,150,0.6);
	border-radius: 10px;
	-webkit-border-radius: 10px;
	box-shadow: 0px 0px 8px rgba(140,70,0,0.5) ;
}
#credits tr td img{
	float: left;
	margin-top: 20px;
	margin-bottom: 20px;
}
#credits tr td a{
	font-size: 80%;
}
#credits tr td h3{
	text-align:center;
} 

#saveImageContent{
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	padding-bottom: 30px;
}
#saveImageContent img{
	max-width: 100%;
	max-height: 100%;
}
