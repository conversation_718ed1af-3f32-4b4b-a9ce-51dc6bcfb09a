<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.0//EN" "http://www.w3.org/TR/REC-html40/strict.dtd">
<html>
<head>
	<title>refNum &rsaquo; dmgutil</title>
	<!--HEAD_BEGIN-->
	<meta http-equiv="Content-Type" content="text/html; charset=UTF8">
	
	

	<script type="text/javascript" src="src/js/refnum.js"></script>

	<script type="text/javascript">
		var _gaq = _gaq || [];
		_gaq.push(['_setAccount', 'UA-********-1']);
		_gaq.push(['_trackPageview']);

		(function() {
			var ga = document.createElement('script'); ga.type = 'text/javascript'; ga.async = true;
			ga.src = ('https:' == document.location.protocol ? 'https://ssl' : 'http://www') + '.google-analytics.com/ga.js';
			var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(ga, s);
		})();
	</script>

	<link type="text/css" href="src/css/refnum.css" rel=stylesheet>
	<!--HEAD_END-->
</head>
<body>

<div id="content">
<!--#include virtual="/src/inc/nav.inc" -->
<script language="javascript">navSelect("dev")</script>

<a href="index.html">
<div id="page_strap_frame"   style="background:url('src/img/page_strap_dmgutil.png');">
<div id="page_strap_content" style="background:url('src/img/page_strap_dmgutil.jpg'); background-repeat: no-repeat; background-position: 22px 16px;"></div></div></a>
<div id="page_body">
<div id="page_content">



<table>
<tr>
<td valign=top>
	<ul id="menu">
	<li class="subtitle">dmgutil</li>
		<li><a href="index.html">Home</a></li>
		<li><a href="licence.html">Licence</a></li>
		<li><a href="version.html">Version History</a></li>
	</ul>
</td>
<td valign=top>



<h1>dmgutil</h1>
<p>
dmgutil is a command line tool for creating, configuring, and compressing Mac OS X
disk images for distribution.
</p>

<p>
dmgutil allows you to automate your build process, preparing a release without pre-existing
templates or user interaction.
</p>

<p>
dmgutil can adjust the size, position, and appearance of Finder windows. It can also apply
custom icons to volumes, folders, or files and assign a license agreement.
</p>

<div class="box_button">
<a href="http://www.refnum.com/files/dmgutil.zip" class="button icon_download"><span><span class="icon"></span>Download</span></a>
<span class="padding20">&nbsp;</span>
<a href="http://github.com/refnum/dmgutil" class="button icon_github"><span><span class="icon"></span>GitHub</span></a>
</div>





<h2>Using dmgutil</h2>
<p>
To create a new disk image for your "MyApp 1.0" release, invoke dmgutil as:
</p>

<div class="box_code_yellow">dmgutil.pl --open --volume="MyApp 1.0" myapp_1.0.dmg
</div>

<p>
Once the disk image has been populated with files, invoke dmgutil to configure it:
</p>

<div class="box_code_yellow">dmgutil.pl --set --iconsize=128 --background=flowers.jpg "/Volumes/MyApp 1.0"
dmgutil.pl --set --x=20 --y=60 --width=300 --height=200  "/Volumes/MyApp 1.0"
</div>

<p>
You can also use dmgutil to set the position of file or folder icons within the window:
</p>

<div class="box_code_yellow">dmgutil.pl --set --x=100 --y=100 "/Volumes/MyApp 1.0/Read Me.rtf"
dmgutil.pl --set --x=200 --y=100 "/Volumes/MyApp 1.0/MyApp.app"
</div>

<p>
dmgutil can also adjust the background color or toolbar visibility of the window, and
apply custom icons to the volume or its contents.
</p>

<p>
Once the volume has been prepared, compress the disk image with:
</p>

<div class="box_code_yellow">dmgutil.pl --close --volume="MyApp 1.0" myapp_1.0.dmg
</div>

<p>
By invoking dmgutil from a post-build shell script, builds can be automatically converted
into a user-friendly disk image for distribution.
</p>





</td>
</tr>
</table>



</div>
</div>
<div id="page_footer"></div>
</div>



<!--FOOTER_BEGIN-->
<div id="footer">
Copyright &copy; 2006-2010 <a href="http://www.refnum.com/">refNum Software</a>
</div>
<!--FOOTER_END-->
</body>
</html>
