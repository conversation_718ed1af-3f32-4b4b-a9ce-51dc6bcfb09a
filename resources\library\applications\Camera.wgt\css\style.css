body {
  background-color: transparent;
  overflow: hidden;
  margin: 0;
  font-family: sans-serif;
}

html, body, .container {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}

img {
  height: auto;
  max-width: 100%;
}

video {
  position: absolute;
  top: 0px;
  left: 0px;
}

#glass {
  position: absolute;
  top: 0px;
  left: 0px;
  /*z-index: 2;*/
  display: block;
  max-width: 100%;
}

.container {
  display: table;
}

.cell {
  display: table-cell;
  margin: 0;
  padding: 0;
  background-color: #ccc;
  text-align: center;
  vertical-align: middle;
}

.content {
  display: block;
  width: 100%;
}

.button {
  align-items: center;
  background-color: #fff;
  border-color: #dbdbdb;
  border-width: 1px;
  color: #363636;
  justify-content: center;
  text-align: center;
  border-radius: 8px;
  display: inline-flex;
  font-weight: bold;
}

.icon {
  width: 30px;
  height: 30px;
  line-height: 30px;
}

.buttons {
  align-items: center;
}

.buttons > * {
  margin: 5px;
}

.is-hidden {
  display: none !important;
}

.overlay {
  display: none;
  position: absolute;
  top: 10px;
  right: 10px;
  flex-wrap: wrap;
  flex-direction: row-reverse;
  max-width: 60%;
  background-color: transparent;
  z-index: 4;
}

.overlay-left {
  display: none;
  position: absolute;
  top: 10px;
  left: 10px;
  flex-wrap: wrap;
  max-width: 40%;
  background-color: transparent;
  z-index: 4;
}

.playpause {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
}

.mediabutton {
  display: none;
  width: 25%;
  height: 25%;
  opacity: 0.9;
  z-index: 3;
}

.nocamera {
  display: none;
  background-color: #ccc;
  justify-content: center;
  height: 100%;
}

/* The container <div> - needed to position the dropdown content */
.dropdown {
  display: inline-block;
  float: right;
}

/* Dropdown Content (Hidden by Default) */
.dropdown-content {
  display: none;
  position: absolute;
  right: 0;
  background-color: #f1f1f1;
  min-width: 160px;
  box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
  z-index: 4;
}

.dropdown-content#videoSelect {
  min-width: 320px;
}

/* Links inside the dropdown */
.dropdown-content div {
  color: black;
  padding: 6px 8px;
  text-decoration: none;
  text-align: left;
}

/* Change color of dropdown links on hover */
.dropdown-content div:hover {background-color: #ddd;}
.dropdown:hover .button {background-color: #ddd;}
.button:hover {background-color: #ddd;}

/* Show the dropdown menu on hover */
.dropdown:hover .dropdown-content {display: block;}

.tooltip {
  position: relative;
  display: inline-block;
  border-bottom: 1px dotted black;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 120px;
  background-color: black;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px 0;
  position: absolute;
  z-index: 5;
  top: -5px;
}

.tooltip .tooltiptext::after {
  content: "";
  position: absolute;
  top: 50%;
  margin-top: -5px;
  border-width: 5px;
  border-style: solid;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
}

/* right */
.tooltip .tooltipright {
  left: 110%;
}

.tooltip .tooltipright::after {
  right: 100%;
  border-color: transparent black transparent transparent;
}

/* left */
.tooltip .tooltipleft {
  right: 110%;
}

.tooltip .tooltipleft::after {
  left: 100%;
  border-color: transparent transparent transparent black;
}

.cropper-container {
  z-index: 3;
}
