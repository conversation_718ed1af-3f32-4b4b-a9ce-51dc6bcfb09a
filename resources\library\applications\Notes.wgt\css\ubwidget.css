* {
    margin: 0;
    padding: 0;
}

body, html{
    margin:0px;
    height: 96%;
}

.ubw-container{
    min-width: 280px;
    min-height: 200px;
    width: 99%;
    height: 100%;
    text-overflow: ellipsis;
    left:0px;
    top:0px;
    margin:0px;
    background-image: url(../images/back.png);
    overflow: hidden;
    border-right: 1px solid rgb(252, 252, 220);
    border-left: 1px solid rgb(232, 232, 220);
}

.ubw-body{
}

.head{
    border-top: 1px solid rgb(252, 252, 220);
    position: relative;
    background-color: transparent;
    font-style: normal;
    color: #263141;
    font-size: 119%;
    width: auto;
    height: 20px;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: normal;
    outline: none;
    border-bottom: rgb(252, 252, 220);	


}
.stickytitle{
    background-color: transparent;
    min-width: 10px;
    max-width: 70%;
    width: 100%;
    padding-top: 4px;
    height: 20px;
    float: left;
    z-index: 3;
    position: relative;
    overflow:hidden;
    margin-left: 10px;
    margin-right: -2px;
    font-style: normal;
    color: #444444;
    font-size: 88%;
    border-style: none;
    outline: none;
    font-family: Arial, Helvetica, sans-serif;
    font-weight: normal;
    text-align: left;
    resize: none;
}

.textField{
    min-height: 50px;
    overflow-y: auto;
    overflow-x: hidden;
    height: auto;
    width: auto;
    margin: 5px;
    padding-right: 4%;
    background-position: 0;
    border-left-style: none;
    border-bottom-style: none;
    border-right-style: none;
    border-top-style: none;
    text-align: left;
    list-style-type: none;
    outline: none;
    word-wrap: break-word;
    font-style: normal;
    font-weight: normal;
    font-family: Arial, Helvetica, sans-serif;
    color:#444444;
}

.ubw-inspector{
    position:absolute;
    background-color:rgb(252, 252, 252);
    border:1px solid #cccccc;	
    line-height:20px;
    font-family:Arial, Helvetica, sans-serif;
    font-weight:normal;
    font-size:20px;
    color:#333333;
}

.ubw-inpubox{
    min-width:28px;
    min-height:37px;
    color:#333333;
    background-image: url(../images/button_out.png);
    border-left:1px solid rgb(231, 231, 231);
    border-right:1px solid rgb(231, 231, 231);
    border-bottom:1px solid rgb(221, 221, 221);
    border-top:1px solid rgb(241, 241, 241);
}

/*BUTTONS*/

.ubw-button-wrapper{
    float:left;
    position:relative;
    /*border:solid 1px yellow;*/
    margin-right:-7px;
    z-index:0;
    font-family:Arial, Helvetica, sans-serif;
    font-weight:normal;
    font-size:30px;
    overflow:visible;
}

.ubw-button-canvas{
    width:auto;
    float:left;
    position:relative;
    overflow:visible;
}

table{
    line-height:90%;
}

.ubw-dropdown{
    margin: 0;
    padding: 0;
    font-size: 15px;
    width:100px;
    list-style: none;
    cursor:pointer;
    float:none;
    margin-left:3px;
}

.ubw-dropdown li.out{
    padding-left: 5px;
    color:#444444;
    border-left:1px solid rgb(231, 231, 233);
    border-right:1px solid rgb(231, 231, 233);
    background-image: url(../images/button_out.gif);
}

.ubw-dropdown li.over{
    padding-left: 5px;
    color:#eeeeee;
    border-left:1px solid rgb(140, 140, 140);
    border-right:1px solid rgb(140, 140, 140);
    background-image: url(../images/button_out_dark.png);
}

.ubw-dropdown li.ubw-dropdown-top-corners{
    border-top:1px solid rgb(241, 241, 244);
}

.ubw-dropdown li.ubw-dropdown-bottom-corners{
    border-bottom:1px solid rgb(221, 221, 223);
}

.ubw-button-body{
    position:relative;
    float:left;

    width:auto;
    height:auto;
    overflow:visible

        text-align:center;
    vertical-align:middle;		

    cursor:pointer;		
}

.ubw-button-content{
    margin:2px;
    height:auto;
    width:auto;
    text-align:center;
    overflow:visible;
}


.ubw-button-over{
    color:#444444;
    background-image: url(../images/button_out.gif);
    border-left:1px solid rgb(221, 221, 221);
    border-right:1px solid rgb(221, 221, 221);
    border-bottom:1px solid rgb(211, 211, 211);
    border-top:1px solid rgb(231, 231, 231);
}

.ubw-button-out{
    color:#555555;
    background-image: url(../images/button_out.gif);
    border-left:2px solid rgb(231, 231, 233);
    border-right:2px solid rgb(231, 231, 233);
    border-bottom:2px solid rgb(221, 221, 223);
    border-top:2px solid rgb(241, 241, 244);
}

span.colored{
    color: #0080ff;
}





.menuElement{
    position: relative;
    float:right;
}



