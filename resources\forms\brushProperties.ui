<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>brushProperties</class>
 <widget class="QFrame" name="brushProperties">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>605</width>
    <height>808</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string/>
  </property>
  <property name="frameShape">
   <enum>QFrame::NoFrame</enum>
  </property>
  <property name="frameShadow">
   <enum>QFrame::Plain</enum>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <item row="6" column="1" colspan="4">
    <widget class="QFrame" name="pressureFrame">
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_4">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QCheckBox" name="pressureSensitiveCheckBox">
        <property name="sizePolicy">
         <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Pen is Pressure Sensitive</string>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="seSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>198</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item row="2" column="1" colspan="4">
    <widget class="QFrame" name="opacityFrame">
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout">
      <item>
       <spacer name="wSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>154</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QLabel" name="opacityLabel">
        <property name="text">
         <string>Opacity</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSlider" name="opacitySlider">
        <property name="minimum">
         <number>20</number>
        </property>
        <property name="maximum">
         <number>100</number>
        </property>
        <property name="value">
         <number>50</number>
        </property>
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="tickPosition">
         <enum>QSlider::TicksAbove</enum>
        </property>
        <property name="tickInterval">
         <number>20</number>
        </property>
       </widget>
      </item>
      <item>
       <spacer name="eSpacer_2">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>156</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
     </layout>
    </widget>
   </item>
   <item row="1" column="1" colspan="4">
    <widget class="QFrame" name="colorFrame">
     <property name="layoutDirection">
      <enum>Qt::LeftToRight</enum>
     </property>
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout">
      <property name="spacing">
       <number>0</number>
      </property>
      <item>
       <widget class="QFrame" name="lightBackgroundFrame">
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="lightBackgroundLayout">
         <item>
          <widget class="QLabel" name="lightBackgroundLabel">
           <property name="text">
            <string>On Light Background</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="UBColorPicker" name="lightBackgroundColorPicker0">
           <property name="minimumSize">
            <size>
             <width>32</width>
             <height>32</height>
            </size>
           </property>
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
      <item>
       <spacer name="horizontalSpacer_3">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item>
       <widget class="QFrame" name="darkBackgroundFrame">
        <property name="frameShape">
         <enum>QFrame::NoFrame</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
        <layout class="QHBoxLayout" name="darkBackgroundLayout">
         <item>
          <widget class="QLabel" name="darkBackgroundLabel">
           <property name="text">
            <string>On Dark Background</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="UBColorPicker" name="darkBackgroundColorPicker0">
           <property name="minimumSize">
            <size>
             <width>32</width>
             <height>32</height>
            </size>
           </property>
           <property name="frameShape">
            <enum>QFrame::StyledPanel</enum>
           </property>
           <property name="frameShadow">
            <enum>QFrame::Raised</enum>
           </property>
          </widget>
         </item>
        </layout>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="4" column="1" colspan="4">
    <widget class="QGroupBox" name="lineWidthGroupBox">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Minimum" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="title">
      <string>Line Width</string>
     </property>
     <layout class="QGridLayout" name="gridLayout_2">
      <item row="1" column="1">
       <spacer name="wSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="1" column="3">
       <spacer name="eSpacer">
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="sizeType">
         <enum>QSizePolicy::Fixed</enum>
        </property>
        <property name="sizeHint" stdset="0">
         <size>
          <width>40</width>
          <height>20</height>
         </size>
        </property>
       </spacer>
      </item>
      <item row="1" column="2">
       <widget class="QSlider" name="fineSlider">
        <property name="minimum">
         <number>5</number>
        </property>
        <property name="maximum">
         <number>500</number>
        </property>
        <property name="value">
         <number>5</number>
        </property>
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="tickPosition">
         <enum>QSlider::TicksAbove</enum>
        </property>
        <property name="tickInterval">
         <number>100</number>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="mediumLabel">
        <property name="text">
         <string>Medium</string>
        </property>
       </widget>
      </item>
      <item row="2" column="2">
       <widget class="QSlider" name="mediumSlider">
        <property name="minimum">
         <number>5</number>
        </property>
        <property name="maximum">
         <number>500</number>
        </property>
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="tickPosition">
         <enum>QSlider::TicksAbove</enum>
        </property>
        <property name="tickInterval">
         <number>100</number>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="strongLabel">
        <property name="text">
         <string>Strong</string>
        </property>
       </widget>
      </item>
      <item row="3" column="2">
       <widget class="QSlider" name="strongSlider">
        <property name="minimum">
         <number>5</number>
        </property>
        <property name="maximum">
         <number>500</number>
        </property>
        <property name="orientation">
         <enum>Qt::Horizontal</enum>
        </property>
        <property name="tickPosition">
         <enum>QSlider::TicksAbove</enum>
        </property>
        <property name="tickInterval">
         <number>100</number>
        </property>
       </widget>
      </item>
      <item row="1" column="4">
       <widget class="UBCircleFrame" name="fineDisplayFrame">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>60</horstretch>
          <verstretch>60</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>60</width>
          <height>60</height>
         </size>
        </property>
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
       </widget>
      </item>
      <item row="2" column="4">
       <widget class="UBCircleFrame" name="mediumDisplayFrame">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>60</horstretch>
          <verstretch>60</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>60</width>
          <height>60</height>
         </size>
        </property>
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
       </widget>
      </item>
      <item row="3" column="4">
       <widget class="UBCircleFrame" name="strongDisplayFrame">
        <property name="sizePolicy">
         <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
          <horstretch>60</horstretch>
          <verstretch>60</verstretch>
         </sizepolicy>
        </property>
        <property name="minimumSize">
         <size>
          <width>60</width>
          <height>60</height>
         </size>
        </property>
        <property name="frameShape">
         <enum>QFrame::StyledPanel</enum>
        </property>
        <property name="frameShadow">
         <enum>QFrame::Raised</enum>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="fineLabel">
        <property name="text">
         <string>Fine</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="3" column="2">
    <spacer name="cSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
   <item row="5" column="1">
    <widget class="QFrame" name="circleFrame">
     <property name="frameShape">
      <enum>QFrame::NoFrame</enum>
     </property>
     <property name="frameShadow">
      <enum>QFrame::Raised</enum>
     </property>
     <layout class="QHBoxLayout" name="horizontalLayout_5">
      <property name="leftMargin">
       <number>0</number>
      </property>
      <property name="topMargin">
       <number>0</number>
      </property>
      <property name="rightMargin">
       <number>0</number>
      </property>
      <property name="bottomMargin">
       <number>0</number>
      </property>
      <item>
       <widget class="QCheckBox" name="circleCheckBox">
        <property name="sizePolicy">
         <sizepolicy hsizetype="MinimumExpanding" vsizetype="Fixed">
          <horstretch>0</horstretch>
          <verstretch>0</verstretch>
         </sizepolicy>
        </property>
        <property name="text">
         <string>Show preview circle from</string>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QSpinBox" name="circleSpinBox"/>
      </item>
      <item>
       <widget class="QLabel" name="pxlabel">
        <property name="text">
         <string>px</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item row="5" column="3">
    <spacer name="cspacerright">
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>198</width>
       <height>20</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>UBColorPicker</class>
   <extends>QFrame</extends>
   <header>gui/UBColorPicker.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>UBCircleFrame</class>
   <extends>QFrame</extends>
   <header>gui/UBCircleFrame.h</header>
   <container>1</container>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections/>
</ui>
